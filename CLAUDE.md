# CLAUDE.md - College Agent Backend Development Guide

This document provides comprehensive information about the College Agent Backend codebase to help <PERSON> understand the project structure, architecture, and development workflows.

## Project Overview

**Project Name**: College Agent Backend (Hi-IdeaGen)  
**Type**: Intelligent Research Report Generation System API  
**Description**: A FastAPI-based backend service that leverages large language models and internet search capabilities to generate high-quality research reports through an automated search-summarize-iterate-search workflow.

## Technology Stack

### Core Framework
- **FastAPI**: High-performance async API framework
- **Python 3.11+**: Primary programming language
- **Uvicorn**: ASGI server for FastAPI

### Database & ORM
- **PostgreSQL 15.6**: Primary database
- **Tortoise ORM**: Async ORM for database operations
- **Aerich**: Database migration tool for Tortoise ORM

### Authentication & Security
- **JWT (JSON Web Tokens)**: Authentication mechanism
- **Passlib + BCrypt**: Password hashing
- **python-jose**: JWT handling

### External Services
- **OpenRouter API**: Unified interface for multiple LLMs
- **SerpAPI**: Google search results processing
- **Jina API**: Content extraction and processing
- **PubMed API**: Academic literature search

### Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration

## Project Structure

```
/Users/<USER>/androidproject/college-agent-backend/
├── app/                           # Main application code
│   ├── api/                      # API layer
│   │   ├── deps.py              # Dependencies (auth, etc.)
│   │   ├── repository/          # Data access layer
│   │   ├── routes/              # API route definitions
│   │   └── schemas/             # Pydantic request/response schemas
│   ├── core/                    # Core configuration
│   │   ├── config.py           # Application settings
│   │   ├── logging.py          # Logging configuration
│   │   ├── redis_client.py     # Redis connection management
│   │   └── security.py         # Security utilities
│   ├── db/                     # Database configuration
│   │   └── config.py          # Tortoise ORM setup
│   ├── models/                 # Database models (ORM)
│   ├── services/               # Business logic layer
│   │   ├── llm_service.py     # LLM integration
│   │   ├── research_service.py # Research workflow
│   │   ├── search_service.py  # Search functionality
│   │   └── prompts.py         # LLM prompts
│   ├── utils/                  # Utility functions
│   └── main.py                # FastAPI application entry
├── migrations/                 # Database migrations
├── scripts/                   # Data initialization scripts
├── test/                     # Test files
├── requirements.txt          # Python dependencies
├── pyproject.toml           # Aerich configuration
├── Dockerfile               # Docker image definition
├── docker-compose.yml       # Production deployment
├── docker-compose-localdb.yml # Local development with DB
├── build.sh                 # Docker build script
├── deploy.sh               # Deployment script
├── entrypoint.sh           # Docker entrypoint
├── run.py                  # Development server
└── wsgi.py                # Production server
```

## Key Architectural Patterns

### 1. Layered Architecture
- **API Layer**: FastAPI routes and request/response handling
- **Service Layer**: Business logic and workflow orchestration
- **Repository Layer**: Data access abstraction
- **Model Layer**: Database entities and relationships

### 2. Dependency Injection
- Uses FastAPI's dependency injection system
- Authentication dependencies in `app/api/deps.py`
- Database connection management through Tortoise ORM

### 3. Async/Await Pattern
- Fully asynchronous codebase
- Non-blocking I/O operations
- Efficient handling of external API calls

### 4. Multi-tenant Architecture
- Organization-based access control
- User role management (Super Admin, Org Admin, Regular User)
- Resource quotas per organization and user

## Development Setup

### Prerequisites
- Python 3.11+
- Docker and Docker Compose
- PostgreSQL 15.6 (via Docker)

### Local Development

1. **Environment Setup**:
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Create virtual environment
   conda create --name college-agent-backend python=3.11.11
   conda activate college-agent-backend
   
   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Database Setup**:
   ```bash
   # Start local PostgreSQL
   docker-compose -f docker-compose-localdb.yml up -d
   ```

3. **Run Application**:
   ```bash
   # Development mode with auto-reload
   python run.py
   
   # Production mode
   python wsgi.py
   ```

4. **Access Points**:
   - API: http://localhost:7000
   - Swagger UI: http://localhost:7000/docs
   - ReDoc: http://localhost:7000/redoc

### Docker Deployment

1. **Build Image**:
   ```bash
   ./build.sh [VERSION] [ENV]
   ```

2. **Deploy Service**:
   ```bash
   ./deploy.sh [VERSION] [ENV]
   ```

## Database Management

### Migration Workflow
- **Development**: Tables auto-created on startup
- **Production**: Use Aerich migrations

```bash
# Generate migration
aerich migrate

# Apply migrations
aerich upgrade

# Initialize (first time)
aerich init-db
```

### Migration Files
- Location: `./migrations/`
- Configuration: `pyproject.toml`
- Auto-upgrade: Handled in `entrypoint.sh`

## API Structure

### Authentication Tiers
1. **Public Routes** (`/api/auth/*`): Login, registration
2. **Authenticated Routes**: Require valid JWT token
3. **Super Admin Routes**: Require super admin role
4. **Non-trial Routes**: Exclude trial users

### Key API Endpoints
- `/api/auth/login` - Admin login
- `/api/users/` - User management
- `/api/organizations/` - Organization management
- `/api/project-configs/` - Project configuration
- `/api/project-reports/` - Report generation
- `/api/model-configs/` - LLM model configuration
- `/api/user-report-usages/` - Usage quota management

## Core Services

### 1. Research Service (`research_service.py`)
- Orchestrates the research workflow
- Manages search-summarize-iterate-search cycle
- Handles literature discovery and analysis
- Implements streaming responses via Server-Sent Events

### 2. LLM Service (`llm_service.py`)
- Integrates with OpenRouter API
- Manages model selection and configuration
- Handles prompt engineering and response processing
- Includes token usage tracking

### 3. Search Service (`search_service.py`)
- SerpAPI integration for web search
- Google Scholar search for academic literature
- PubMed integration for medical research
- Content extraction and summarization

## Configuration Management

### Environment Variables
Key configurations in `.env`:

```env
# Server
HOST=0.0.0.0
PORT=7000

# Database
DATABASE_URL=postgres://postgres:123456@localhost:5432/hi_ideagen

# Security
SECRET_KEY=your-secret-key
DEFAULT_ADMIN_USERNAME=idea_admin
DEFAULT_ADMIN_PASSWORD=secure-password

# External APIs
SERPAPI_API_KEY=your-serpapi-key
JINA_API_KEY=your-jina-key
PUBMED_EMAIL=<EMAIL>

# Features
LOG_LEVEL=INFO
```

### Model Configuration
- Configurable LLM parameters (temperature, top_k, top_p)
- Per-organization model access control
- Usage quotas and rate limiting

## Logging System

### Features
- **Loguru-based**: Advanced logging with automatic rotation
- **Time-based Structure**: Daily directories, hourly files
- **Compression**: Automatic ZIP compression of old logs
- **Retention**: Configurable retention period (default 30 days)

### Log Structure
```
logs/
├── 2025-01-24/
│   ├── 2025-01-24_00.log
│   ├── 2025-01-24_01.log
│   └── ...
└── ...
```

### Key Logged Events
- Research workflow progress
- LLM API interactions
- Search operations
- Authentication events
- Error handling and debugging

## Testing

### Test Structure
- Location: `./test/`
- Framework: Standard Python unittest
- Coverage: Logging configuration and core functionality

### Running Tests
```bash
python -m pytest test/
```

## Data Models

### Key Entities
- **User**: System users with role-based access
- **Organization**: Multi-tenant organization structure
- **Research**: Research projects and reports
- **ProjectConfig**: Project configuration and parameters
- **ModelConfig**: LLM model configurations
- **UserReportUsage**: Usage tracking and quotas
- **LiteratureLibrary**: Academic literature management

### Relationships
- Users belong to Organizations
- Organizations have ModelConfigs
- Research projects link to ProjectConfigs
- Usage tracking per User and Organization

## Security Considerations

### Authentication
- JWT-based stateless authentication
- Role-based access control (RBAC)
- API key authentication for external access

### Data Protection
- Password hashing with BCrypt
- Encrypted sensitive configuration
- SQL injection prevention via ORM

### Rate Limiting
- Usage quotas per user/organization
- LLM token consumption tracking
- Search API rate limiting

## Development Guidelines

### Code Style
- Follow Python PEP 8 conventions
- Use type hints throughout
- Async/await for I/O operations
- Comprehensive error handling

### Git Workflow
- Feature branches for development
- Database migration management
- Pre-commit hooks for migration generation

### Error Handling
- Custom exception handlers
- Structured error responses
- Comprehensive logging for debugging

## Deployment Environments

### Development (dev)
- Local PostgreSQL via Docker
- Debug logging enabled
- Auto-reload enabled

### Testing (test)
- Shared test database
- Integration testing
- Performance monitoring

### Production (prod)
- High availability setup
- Performance optimizations
- Security hardening
- Comprehensive monitoring

## Performance Considerations

### Database
- Connection pooling via Tortoise ORM
- Async operations for scalability
- Proper indexing on query patterns

### External APIs
- Concurrent request handling
- Rate limiting compliance
- Caching strategies for repeated requests

### Resource Management
- Memory-efficient streaming responses
- Proper connection cleanup
- Configurable timeout handling

## Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URL and PostgreSQL service
2. **Migration Errors**: Ensure proper Aerich setup and migration order
3. **API Key Issues**: Verify external service API keys and quotas
4. **Port Conflicts**: Default port 7000, check for conflicts

### Debug Mode
- Set `LOG_LEVEL=DEBUG` in environment
- Enable detailed request/response logging
- Monitor external API calls and responses

### Health Checks
- Root endpoint `/` provides service status
- Database connectivity verification
- External service availability checks

## Future Development

### Planned Features
- Enhanced literature analysis
- Multi-language support
- Advanced report customization
- Real-time collaboration features

### Scalability Considerations
- Microservices architecture transition
- Database sharding strategies
- Caching layer implementation
- Load balancing setup

---

This document should be updated as the codebase evolves. For questions about specific implementations, refer to the inline documentation in the source code or consult the development team.