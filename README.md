# Co-mpanion API

基于FastAPI和OpenRouter实现的智能研究报告生成系统API服务。

## 项目概述

Co-mpanion 是一个智能研究报告生成系统，通过结合大语言模型和互联网搜索能力，实现自动化研究和报告生成。系统采用基于搜索-总结-迭代-搜索的流程，能够高效地生成高质量研究报告。

## 功能特性

- **智能研究报告生成**：基于搜索-总结-迭代-搜索流程的智能研究报告生成
- **流式响应**：支持Server-Sent Events，实时呈现研究和报告生成过程
- **多级权限控制**：支持超级管理员、机构管理员、普通用户的三级权限体系
- **智能配额管理**：机构级别和用户级别的动态配额分配与管理系统
- **用户与权限管理**：完整的用户管理和API密钥认证系统
- **短信验证服务**：支持手机号验证码登录/注册，集成阿里云短信服务
- **项目管理**：支持项目配置、项目主体、项目成员等多维度管理
- **数据管理**：报告和研究数据的存储与管理
- **自动化搜索**：集成SerpAPI，实现自动化内容提取

## 技术栈

- **FastAPI**：高性能异步API框架，提供现代化的API开发体验
- **Tortoise ORM**：异步ORM数据库操作，与FastAPI完美配合
- **PostgreSQL**：可靠的关系型数据库，用于数据持久化存储
- **OpenRouter API**：统一接口调用多种大语言模型
- **SerpAPI**：处理和解析搜索结果
- **Server-Sent Events**：实现流式响应，提升用户体验

## 环境要求

- Python 3.11+
- Docker 和 Docker Compose
- PostgreSQL 15.6 (通过Docker提供)

## 安装和运行

### 1. 克隆项目并进入目录

```bash
git clone <repository-url>
cd Co-mpanion-api
```

### 2. 配置环境变量

```bash
cp .env.example .env
```

编辑`.env`文件，确保以下关键配置正确：

- `DATABASE_URL`: 数据库连接URL
- `SECRET_KEY`: JWT加密密钥
- `DEFAULT_ADMIN_USERNAME`: 默认管理员用户名
- `DEFAULT_ADMIN_PASSWORD`: 默认管理员密码
- `SERPAPI_API_KEY`: SerpAPI密钥

#### 短信服务配置

**生产环境配置：**
```bash
# 阿里云短信服务配置
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
SMS_SIGN_NAME=你的短信签名
SMS_TEMPLATE_CODE=SMS_123456789
SMS_CODE_EXPIRE_TIME=300
SMS_SEND_INTERVAL=60
SMS_TEST_MODE=OFF
```

**测试/开发环境配置：**
```bash
# 启用测试模式，使用固定验证码
SMS_TEST_MODE=ON
SMS_TEST_CODE=654321
# 阿里云配置可以为空
ALIBABA_CLOUD_ACCESS_KEY_ID=
ALIBABA_CLOUD_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=
SMS_TEMPLATE_CODE=
```

> **测试模式说明：** 当 `SMS_TEST_MODE=ON` 时，系统将使用 `SMS_TEST_CODE` 配置的固定验证码（默认654321），无需配置阿里云短信服务即可进行开发和测试。

### 3. 启动数据库

```bash
docker-compose up -d
```

这将启动PostgreSQL数据库，默认配置：
- 数据库名：hi_ideagen
- 用户名：postgres
- 密码：123456
- 端口：5432

### 4. 创建并激活虚拟环境（推荐）

```bash
# 使用conda
conda create -n co-mpanion python=3.11
conda activate co-mpanion

# 或使用venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
```

### 5. 安装依赖

```bash
pip install -r requirements.txt
```

### 6. 数据库初始化（可选）

开发环境可以直接运行应用，数据库表会自动创建。如果需要使用数据库迁移功能，可以执行：

```bash
aerich init-db
```
migrations/models里面是数据库迁移过程需要用的脚本。本地开发每次修改了表结构之后在提交代码之前需要执行（始终让一个开发去生成migration脚本，或者用一台服务器去生成migration脚本）：
```bash
aerich migrate
```
或者可以将下面的脚本放置在项目根目录/.git/hooks/pre-commit文件里面（注意.git/hooks文件夹本身有个pre-commit.sample的文件，你copy一份重命名为pre-commit），这样你每次git push之前会自动执行aerich migrate命令:
```bash
#!/bin/bash
# pre-commit hook: 智能检测是否需要 aerich migrate

echo "Checking for database changes..."

# 先 dry-run 一次 aerich migrate，检查有没有需要生成的迁移
DRY_RUN_OUTPUT=$(aerich migrate --name temp_migrate --no-generate 2>&1)

# 判断输出中是否有 "No changes detected" 字样
if echo "$DRY_RUN_OUTPUT" | grep -q "No changes detected"; then
    echo "No changes detected. Skip migrate."
else
    echo "Changes detected. Running aerich migrate..."
    aerich migrate
    git add ./migrations
    echo "Migrations generated and added to git."
fi

exit 0
```
在测试服务器或者服务器上，你可以在项目文件的根目录执行：
```bash
aerich upgrade
```
来进行数据库的迁移动作。现在项目是把这个动作放置在了Docker里面执行。参见entrypoint.sh和Dockerfile文件。
### 7. 运行应用

```bash
python run.py
```

应用将在 http://localhost:7000 启动

## 验证安装

1. 访问API文档：
   - Swagger UI: http://localhost:7000/docs
   - ReDoc: http://localhost:7000/redoc

2. 使用默认管理员账户登录（如果已在.env中配置）

## 项目结构

```
app/
├── api/            # API路由和端点
│   ├── deps.py     # 依赖项（认证等）
│   └── routes/     # 各模块路由定义
├── core/           # 核心配置
│   ├── config.py   # 应用配置
│   ├── logging.py  # 日志配置
│   └── security.py # 安全相关
├── db/             # 数据库相关
│   └── config.py   # 数据库配置
├── models/         # 数据模型
│   ├── api_key.py  # API密钥模型
│   ├── user.py     # 用户模型
│   └── ...         # 其他数据模型
├── schemas/        # Pydantic模式
├── services/       # 业务逻辑
│   ├── llm_service.py      # 大语言模型服务
│   ├── research_service.py # 研究服务
│   └── search_service.py   # 搜索服务
├── utils/          # 工具函数
└── main.py         # 应用入口
└── 灾难性回溯文本.md         #如果有对正文进行正则匹配的情况下，最好用这个文档测试一下正则模式，防止出现CPU爆满服务挂掉的情况 
```

## API概览

### 认证相关
- **/api/auth/login** - 管理员登录
- **/api/sms/send-code** - 发送短信验证码
- **/api/sms/mobile-login** - 手机号验证码登录/注册

### 用户和权限管理
- **/api/users/** - 用户管理
- **/api/organizations/** - 机构管理
- **/api/roles/** - 角色管理
- **/api/user-report-usages/** - 用户使用次数配额管理
- **/api/api-keys/** - API密钥管理

### 项目和报告管理
- **/api/reports/** - 研究报告管理
- **/api/research/** - 研究项目管理
- **/api/project-configs/** - 项目配置管理
- **/api/project-leaders/** - 项目主体管理
- **/api/project-members/** - 项目成员管理
- **/api/project-member-joins/** - 项目成员关联管理
- **/api/project-reports/** - 项目大纲和报告生成
- **/api/project-downloads/** - 项目文件下载
- **/api/model-configs/** - 模型配置管理

### SMS API 使用示例

**发送验证码：**
```bash
curl -X POST "http://localhost:8001/api/sms/send-code" \
  -H "Content-Type: application/json" \
  -d '{"mobile": "13800138000"}'
```

**手机号登录：**
```bash
curl -X POST "http://localhost:8001/api/sms/mobile-login" \
  -H "Content-Type: application/json" \
  -d '{"mobile": "13800138000", "code": "654321"}'
```

> **注意：** 在测试模式下（`SMS_TEST_MODE=ON`），使用配置的固定验证码（默认654321）即可完成登录。

## 开发指南

### 添加新功能

1. 在`app/models/`中定义数据模型
2. 在`app/schemas/`中定义API请求/响应模式
3. 在`app/services/`中实现业务逻辑
4. 在`app/api/routes/`中定义API端点
5. 在`app/api/routes/__init__.py`中注册路由

## 环境变量说明

在`.env`文件中可以配置以下参数：

- `HOST` - 服务监听地址，默认为0.0.0.0
- `PORT` - 服务端口，默认为7000
- `DATABASE_URL` - 数据库连接URL
- `SECRET_KEY` - JWT加密密钥
- `DEFAULT_ADMIN_USERNAME` - 默认管理员用户名
- `DEFAULT_ADMIN_PASSWORD` - 默认管理员密码
- `LOG_LEVEL` - 日志级别（DEBUG/INFO/WARNING/ERROR/CRITICAL）
- `LOG_ROOT_DIR` - 日志目录，宿主机挂载目录路径
- `LOG_DIR` - 应用程序内部日志目录路径
- `LOG_RETENTION_DAYS` - 日志保留天数，默认30天
- `TZ` - 时区设置，默认为Asia/Shanghai（东八区）
- `SERPAPI_API_KEY` - SerpAPI密钥
- `JINA_API_KEY` - Jina API密钥
- `REDIS_URL`：Redis 连接地址，示例：`redis://localhost:6379/0`，用于单点登录会话存储

## 日志系统

系统使用 loguru 库实现了高级日志管理功能，支持以下特性：

### 日志分割策略
- **按天分目录**：每天的日志存储在独立目录中，格式为 `YYYY-MM-DD`
- **按小时分文件**：每小时生成一个日志文件，格式为 `YYYY-MM-DD_HH.log`

### 日志目录结构
```
logs/
├── 2025-01-24/
│   ├── 2025-01-24_00.log  # 00:00-00:59 的日志
│   ├── 2025-01-24_01.log  # 01:00-01:59 的日志
│   └── ...
├── 2025-01-25/
│   └── ...
└── ...
```

### 日志格式
日志采用类似 Spring Boot 的标准格式：
```
2025-01-24 14:30:15.123  INFO 12345 --- [Thread-1] app.services.research_service : 开始生成研究报告，项目ID: 123
```

### 日志配置
- `LOG_LEVEL`: 日志级别（DEBUG/INFO/WARNING/ERROR/CRITICAL）

### 日志保留策略
- **统一保留策略**：默认保留 30 天（可通过 `LOG_RETENTION_DAYS` 配置），自动压缩为 zip 格式
- **日志压缩**：每小时轮转时，上一个小时的日志文件立即压缩为 zip 格式
- **实时输出**：开发环境同时输出到控制台，支持彩色显示

### 容器挂载
在 Docker 部署时，日志目录会挂载到宿主机：
```bash
# docker-compose.yml 中的配置
volumes:
  - ./logs:/app/logs  # 日志目录挂载到当前目录的logs文件夹
```

**注意**：在生产环境中，建议将 `./logs` 改为绝对路径，如 `/var/log/Co-mpanion:/app/logs`

日志记录涵盖以下关键模块：

- 研究服务（research_service）：记录研究流程各阶段的进展
- LLM服务（llm_service）：记录与大语言模型的交互
- 搜索服务（search_service）：记录搜索请求和结果处理

## Docker部署

项目提供了完整的Docker支持，可以通过以下命令进行部署：

```bash
# 构建并启动所有服务
docker-compose up -d

# 仅启动数据库（开发环境）
docker-compose -f docker-compose-localdb.yml up -d
```
## 数据库迁移脚本
1、migrations文件夹下面的脚本是创建表、增删表字段的。
2、scripts下面的脚本是修复数据和创建初始化数据的脚本。

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request
