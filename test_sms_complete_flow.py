#!/usr/bin/env python3
"""
完整的SMS测试流程脚本
测试发送验证码 -> 手机号登录的完整流程
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8001"  # 根据实际端口调整
SMS_SEND_CODE_URL = f"{BASE_URL}/api/sms/send-code"
SMS_MOBILE_LOGIN_URL = f"{BASE_URL}/api/sms/mobile-login"

# 测试配置
TEST_MOBILE = "13800138000"
TEST_CODE = "654321"  # 与SMS_TEST_CODE配置一致

def test_complete_sms_flow():
    """测试完整的SMS验证流程"""
    print("=" * 60)
    print("SMS 完整验证流程测试")
    print("=" * 60)
    print(f"测试手机号: {TEST_MOBILE}")
    print(f"测试验证码: {TEST_CODE}")
    print()
    
    # 步骤1: 发送验证码
    print("步骤1: 发送验证码")
    print("-" * 30)
    
    send_code_data = {
        "mobile": TEST_MOBILE
    }
    
    try:
        response = requests.post(
            SMS_SEND_CODE_URL,
            json=send_code_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"请求URL: {SMS_SEND_CODE_URL}")
        print(f"请求数据: {json.dumps(send_code_data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 验证码发送成功！")
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查是否是测试模式
            if "测试模式" in response_data.get("data", {}).get("message", ""):
                print("🧪 检测到测试模式，使用固定验证码")
            
        elif response.status_code == 403:
            print("❌ 403 Forbidden - 接口被拦截！")
            print(f"响应内容: {response.text}")
            return False
        else:
            print(f"⚠️  发送验证码失败 - 状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 发送验证码异常: {str(e)}")
        return False
    
    print()
    
    # 等待一下（模拟用户输入验证码的时间）
    print("等待2秒（模拟用户输入验证码）...")
    time.sleep(2)
    print()
    
    # 步骤2: 使用验证码登录
    print("步骤2: 手机号验证码登录")
    print("-" * 30)
    
    login_data = {
        "mobile": TEST_MOBILE,
        "code": TEST_CODE
    }
    
    try:
        response = requests.post(
            SMS_MOBILE_LOGIN_URL,
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"请求URL: {SMS_MOBILE_LOGIN_URL}")
        print(f"请求数据: {json.dumps(login_data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 手机号登录成功！")
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 提取token信息
            if response_data.get("success") and response_data.get("data"):
                data = response_data["data"]
                access_token = data.get("access_token")
                user_id = data.get("user_id")
                username = data.get("username")
                is_new_user = data.get("is_new_user")
                
                print()
                print("🎉 登录信息:")
                print(f"   用户ID: {user_id}")
                print(f"   用户名: {username}")
                print(f"   是否新用户: {is_new_user}")
                print(f"   Token: {access_token[:50]}..." if access_token else "   Token: 无")
                
                return True
            else:
                print("⚠️  登录响应格式异常")
                return False
                
        elif response.status_code == 403:
            print("❌ 403 Forbidden - 接口被拦截！")
            print(f"响应内容: {response.text}")
            return False
        else:
            print(f"⚠️  登录失败 - 状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False

def test_wrong_code():
    """测试错误验证码"""
    print("\n" + "=" * 60)
    print("测试错误验证码（应该失败）")
    print("=" * 60)
    
    # 先发送验证码
    send_code_data = {"mobile": TEST_MOBILE}
    try:
        requests.post(SMS_SEND_CODE_URL, json=send_code_data, timeout=10)
    except:
        pass
    
    # 使用错误的验证码登录
    login_data = {
        "mobile": TEST_MOBILE,
        "code": "123456"  # 错误的验证码
    }
    
    try:
        response = requests.post(
            SMS_MOBILE_LOGIN_URL,
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"使用错误验证码: {login_data['code']}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            if not response_data.get("success"):
                print("✅ 正确拒绝了错误的验证码")
                print(f"错误信息: {response_data.get('error', '未知错误')}")
            else:
                print("❌ 错误地接受了错误的验证码")
        else:
            print(f"⚠️  意外的状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

if __name__ == "__main__":
    print("SMS 完整流程测试")
    print("请确保:")
    print("1. 后端服务已启动在 http://localhost:8001")
    print("2. 环境变量 SMS_TEST_MODE=ON")
    print("3. 环境变量 SMS_TEST_CODE=654321")
    print()
    
    # 测试完整流程
    success = test_complete_sms_flow()
    
    if success:
        # 测试错误验证码
        test_wrong_code()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
