# 开发测试环境示例

# 服务配置
HOST=0.0.0.0
PORT=7000

# 数据库配置信息
DATABASE_URL=postgres://postgres:123456@localhost:5432/hi_ideagen
# 安全配置，用于生成JWT令牌，请勿泄露，用于加密和安全处理的密钥，相当于盐值
SECRET_KEY=your_secret_key_here

# 加密用的key
CRYPTO_BASE_KEY=

# 默认管理员配置
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=strong_password_here

# 默认管理员角色，第一次启动时，会自动创建一个超级管理员用户，这是角色代码
DEFAULT_ADMIN_ROLE=super_admin


# 研究配置
# SerpAPI API Key，用于Google搜索
SERPAPI_API_KEY=your_serpapi_api_key
# Jina API Key，用于Jina搜索
JINA_API_KEY=your_jina_api_key

# PubMed配置
PUBMED_TOOL_NAME=College-Agent
PUBMED_EMAIL=

# 日志配置
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
# 日志目录，宿主机挂载目录，用绝对路径表示，跟上面的配置不一样，这个是Docker宿主机挂载目录
LOG_ROOT_DIR=/home/<USER>/co-mpanion-dev/college-files/logs
# 项目的日志文件具体输出的路径目录，本地开发用相对路径./logs，Docker用绝对路径/app/logs
LOG_DIR=./logs
# 日志保留天数，超过此天数的日志文件将被自动删除
LOG_RETENTION_DAYS=30
# 时区设置，Docker容器时区配置，默认东八区
TZ=Asia/Shanghai
# 体验用户允许看到的最大文字（报告和大纲都一样）
TRIAL_USER_MAX_TEXT=10000


# 搜索配置
SEARCH_RESULTS_LIMIT=10
# 研究迭代次数限制（搜索-分析-改进查询的最大循环次数，每次循环系统会根据已获取的信息生成新的搜索查询，直到达到信息充分或达到迭代上限）
RESEARCH_ITERATION_LIMIT=2
# 谷歌搜索相关上下文的时候允许的最大搜索次数
MAX_SEARCH_QUERIES=10
# 不开启文献总结的情况下，允许收集的最大contexts数
MAX_CONTEXTS=5
# 开启文献总结的情况下，允许收集的最大contexts数（contexts + literature的总数）
MAX_LITERATURE_AND_CONTEXTS=35
# 每次生成报告的最大搜索参考文献数
MAX_LITERATURES_COUNT=35

# 幻觉审查的google搜索条目数
MAX_HALLUCINATION_DETECTION_SEARCH=10

#谷歌搜索引擎
SEARCH_ENGINE=google  
#参考文献谷歌搜索引擎
LITERATURE_LIBRARY_SEARCH_ENGINE=google_scholar

######LLM参数
# 温度
LLM_DEFAULT_TEMPERATURE=1
# 上采样
LLM_DEFAULT_TOP_K=80
# 上采样
LLM_DEFAULT_TOP_P=0.6
# dify应用的api基础地址（不要以“/”结尾）
DIFY_API_URL=""
# dify应用的密钥
DIFY_API_KEY=""

# 阿里云短信服务配置
# 阿里云AccessKey ID，用于短信服务认证
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id_here
# 阿里云AccessKey Secret，用于短信服务认证
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret_here
# 短信签名名称，在阿里云控制台申请的签名
SMS_SIGN_NAME=你的短信签名
# 短信模板代码，在阿里云控制台申请的模板
SMS_TEMPLATE_CODE=SMS_123456789
# 验证码有效期（秒），默认5分钟
SMS_CODE_EXPIRE_TIME=300
# 同一手机号发送短信间隔（秒），防止频繁发送
SMS_SEND_INTERVAL=60

# 短信测试模式配置
# 是否启用测试模式（ON/OFF），启用后可使用固定验证码绕过阿里云短信服务
SMS_TEST_MODE=ON
# 测试用固定验证码，在测试模式下使用此验证码可直接通过验证
SMS_TEST_CODE=654321

# Redis配置
# Redis连接URL，包含主机、端口、数据库
REDIS_URL=redis://localhost:6379/10
# Redis密码（如果Redis设置了密码认证）
# REDIS_PASSWORD=your_redis_password_here
