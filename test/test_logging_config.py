# 验证日志配置的脚本
import os
from app.core.config import settings
from app.core.logging import get_logger

def test_logging_config():
    """测试日志配置是否正确读取"""
    logger = get_logger(__name__)
    
    print("=== 日志配置验证 ===")
    print(f"日志级别: {settings.LOG_LEVEL}")
    print(f"日志目录: {settings.LOG_DIR}")
    print(f"日志保留天数: {settings.LOG_RETENTION_DAYS}")
    
    # 记录测试日志
    logger.info(f"配置验证 - 统一日志保留{settings.LOG_RETENTION_DAYS}天")
    logger.error(f"配置验证 - 错误日志也保留{settings.LOG_RETENTION_DAYS}天")
    
    print("\n=== 日志压缩说明 ===")
    print("📋 日志压缩时间：每小时轮转时，上一个小时的日志文件立即压缩")
    print("📁 压缩格式：.log.zip")
    print("⏰ 压缩触发：每到新的小时（如从18:59进入19:00时）")
    print("📂 当前小时文件：保持未压缩状态（正在写入）")
    print("📂 历史小时文件：全部压缩为zip格式")
    
    print(f"\n✅ 日志配置验证完成！")
    return True

if __name__ == "__main__":
    test_logging_config() 