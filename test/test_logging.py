# 测试日志系统的脚本
import asyncio
import time
from app.core.logging import get_logger

# 创建测试logger
logger = get_logger(__name__)

def test_logging_levels():
    """测试不同级别的日志"""
    logger.debug("这是一个DEBUG级别的日志")
    logger.info("这是一个INFO级别的日志")
    logger.warning("这是一个WARNING级别的日志")
    logger.error("这是一个ERROR级别的日志")
    logger.critical("这是一个CRITICAL级别的日志")

def test_logging_with_variables():
    """测试带变量的日志"""
    user_id = 12345
    operation = "生成研究报告"
    duration = 2.5
    
    logger.info(f"用户 {user_id} 开始执行操作: {operation}")
    time.sleep(1)  # 模拟操作
    logger.info(f"操作完成，耗时: {duration}秒")

def test_exception_logging():
    """测试异常日志"""
    try:
        # 故意制造一个错误
        result = 10 / 0
    except Exception as e:
        logger.exception(f"发生了一个错误: {str(e)}")

def test_complex_logging():
    """测试复杂场景的日志"""
    logger.info("开始模拟复杂业务场景")
    
    # 模拟用户登录
    logger.info("用户登录: username=test_user, ip=*************")
    
    # 模拟业务操作
    logger.info("开始处理业务请求: request_id=REQ_001")
    logger.debug("请求参数: {'type': 'report', 'format': 'pdf'}")
    
    # 模拟数据库操作
    logger.debug("执行数据库查询: SELECT * FROM projects WHERE user_id = 123")
    logger.info("数据库查询完成，返回 5 条记录")
    
    # 模拟API调用
    logger.info("调用外部API: https://api.example.com/generate")
    logger.warning("API响应时间较长: 3.2秒")
    
    # 模拟完成
    logger.info("业务请求处理完成: request_id=REQ_001, status=success")

if __name__ == "__main__":
    print("开始测试日志系统...")
    
    # 测试基本日志级别
    print("1. 测试日志级别...")
    test_logging_levels()
    
    # 测试带变量的日志
    print("2. 测试带变量的日志...")
    test_logging_with_variables()
    
    # 测试异常日志
    print("3. 测试异常日志...")
    test_exception_logging()
    
    # 测试复杂场景
    print("4. 测试复杂业务场景...")
    test_complex_logging()
    
    print("日志测试完成！请检查 logs/ 目录查看生成的日志文件。")
    print(f"日志文件应该位于: logs/{time.strftime('%Y-%m-%d')}/{time.strftime('%Y-%m-%d_%H')}.log") 