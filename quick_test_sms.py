#!/usr/bin/env python3
"""
快速测试SMS配置的脚本
用于验证SMS测试模式是否正确配置
"""

import os
import sys

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    print("-" * 40)
    
    # 检查关键环境变量
    required_vars = {
        'SMS_TEST_MODE': 'ON',
        'SMS_TEST_CODE': '654321'
    }
    
    missing_vars = []
    incorrect_vars = []
    
    for var, expected in required_vars.items():
        value = os.environ.get(var)
        if value is None:
            missing_vars.append(var)
        elif value != expected:
            incorrect_vars.append(f"{var}={value} (期望: {expected})")
        else:
            print(f"✅ {var}={value}")
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    if incorrect_vars:
        print(f"⚠️  配置不匹配: {', '.join(incorrect_vars)}")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def check_imports():
    """检查必要的模块导入"""
    print("\n🔍 检查模块导入...")
    print("-" * 40)
    
    try:
        from app.core.config import settings
        print(f"✅ SMS_TEST_MODE: {settings.SMS_TEST_MODE}")
        print(f"✅ SMS_TEST_CODE: {settings.SMS_TEST_CODE}")
        
        if settings.SMS_TEST_MODE != "ON":
            print("❌ SMS_TEST_MODE 未设置为 ON")
            return False
            
        if settings.SMS_TEST_CODE != "654321":
            print(f"⚠️  SMS_TEST_CODE 设置为: {settings.SMS_TEST_CODE} (期望: 654321)")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        return False

def test_sms_service():
    """测试SMS服务"""
    print("\n🔍 测试SMS服务...")
    print("-" * 40)
    
    try:
        from app.services.sms_service import sms_service
        from app.core.config import settings
        
        # 测试发送验证码（测试模式）
        print("测试发送验证码...")
        result = await sms_service.send_verification_code("13800138000")
        
        if result["success"]:
            print("✅ 发送验证码测试通过")
            print(f"   消息: {result['message']}")
        else:
            print("❌ 发送验证码测试失败")
            print(f"   错误: {result['message']}")
            return False
        
        # 测试验证码验证
        print("测试验证码验证...")
        is_valid = await sms_service.verify_code("13800138000", settings.SMS_TEST_CODE)
        
        if is_valid:
            print("✅ 验证码验证测试通过")
        else:
            print("❌ 验证码验证测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SMS服务测试失败: {e}")
        return False

async def run_async_tests():
    """运行异步测试"""
    return test_sms_service()

def main():
    """主函数"""
    print("SMS 配置快速测试")
    print("=" * 50)
    
    # 检查环境配置
    if not check_environment():
        print("\n❌ 环境配置检查失败")
        print("请确保设置了以下环境变量:")
        print("export SMS_TEST_MODE=ON")
        print("export SMS_TEST_CODE=654321")
        print("\n或在 .env 文件中添加:")
        print("SMS_TEST_MODE=ON")
        print("SMS_TEST_CODE=654321")
        sys.exit(1)
    
    # 检查模块导入
    if not check_imports():
        print("\n❌ 模块导入检查失败")
        print("请确保项目依赖已正确安装")
        sys.exit(1)
    
    # 运行异步测试
    try:
        import asyncio
        result = asyncio.run(run_async_tests())
        if result:
            print("\n🎉 所有测试通过！")
            print("SMS测试模式配置正确，可以使用固定验证码 654321 进行测试")
        else:
            print("\n❌ SMS服务测试失败")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 异步测试执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
