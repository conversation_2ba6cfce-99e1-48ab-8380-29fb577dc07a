version: '3.8'

services:
  api:
    image: college-agent-api-${ENV:-dev}:${VERSION:-latest}
    container_name: ${CONTAINER_NAME:-college-agent-api-${ENV:-dev}}
    restart: "no"
    working_dir: /app
    ports:
      - ${PORT}:${PORT}
    volumes:
      # 项目的大纲、正文文件
      - ${STATICS_PATH}/project:/app/llm_file
      # 图片文件
      - ${STATICS_PATH}/images:/app/images
      # 附件文件
      - ${STATICS_PATH}/attachments:/app/attachments
      # 日志目录挂载
      - ${LOG_ROOT_DIR}:/app/logs
    env_file:
      - ${ENV_FILE}
    networks:
      - college-api-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - LOG_LEVEL=DEBUG
      # 设置容器时区为东八区
      - TZ=Asia/Shanghai
    user: "${HOST_UID}:${HOST_GID}"

networks:
  college-api-network:
    driver: bridge
