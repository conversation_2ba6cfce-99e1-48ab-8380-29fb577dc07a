import base64
import binascii
from gmssl import sm3
from gmssl.sm4 import CryptSM4, SM4_ENCRYPT, SM4_DECRYPT
from app.core.config import settings
from app.core.logging import get_logger

# 配置日志
logger = get_logger(__name__)

# 从配置中获取基础密钥
BASE_KEY = settings.CRYPTO_BASE_KEY

# 正确处理sm3_hash输入，转换为list而不是bytes
def bytes_to_list(bytes_str):
    """将bytes转换为整数列表"""
    return list(bytes_str)

# 实现与前端一致的SM3哈希函数
def sm3_compatible(text):
    """与前端JS兼容的SM3哈希实现"""
    # 将字符串转为UTF-8编码的字节列表
    text_bytes = text.encode('utf-8')
    text_list = bytes_to_list(text_bytes)
    # 调用gmssl的sm3_hash函数
    return sm3.sm3_hash(text_list)

try:
    # 计算sm3哈希值 - 以十六进制字符串形式返回，与前端保持一致
    sm3_digest_hex = sm3_compatible(BASE_KEY)
    # 分割key和iv，与前端保持一致
    key_hex = sm3_digest_hex[:len(sm3_digest_hex)//2]
    iv_hex = sm3_digest_hex[len(sm3_digest_hex)//2:]
    
    # 打印调试信息
    logger.info(f"SM3摘要: {sm3_digest_hex}")
    logger.info(f"密钥: {key_hex}")
    logger.info(f"IV: {iv_hex}")
except Exception as e:
    logger.error(f"初始化SM3哈希时出错: {str(e)}")
    # 提供默认值，避免程序崩溃
    key_hex = "00" * 16
    iv_hex = "00" * 16

def sm4_encrypt(data: str) -> str:
    """
    使用SM4算法加密数据
    :param data: 待加密的字符串
    :return: Base64编码的加密结果
    """
    if not data:
        return ""
    
    try:
        # 创建SM4加密对象
        crypt_sm4 = CryptSM4()
        crypt_sm4.set_key(binascii.unhexlify(key_hex), SM4_ENCRYPT)
        
        # 使用CBC模式加密
        encrypt_data = crypt_sm4.crypt_cbc(binascii.unhexlify(iv_hex), data.encode())
        # 转为Base64编码
        return base64.b64encode(encrypt_data).decode()
    except Exception as e:
        logger.error(f"加密失败: {str(e)}")
        return ""

def sm4_decrypt(encrypted_data: str) -> str:
    """
    使用SM4算法解密数据
    :param encrypted_data: Base64编码的加密字符串
    :return: 解密后的原文
    """
    if not encrypted_data:
        return ""
        
    # 解码Base64
    try:
        data = base64.b64decode(encrypted_data)
        
        # 创建SM4解密对象
        crypt_sm4 = CryptSM4()
        crypt_sm4.set_key(binascii.unhexlify(key_hex), SM4_DECRYPT)
        
        # 使用CBC模式解密
        decrypted_data = crypt_sm4.crypt_cbc(binascii.unhexlify(iv_hex), data)
        return decrypted_data.decode()
    except Exception as e:
        # 解密失败记录日志
        logger.error(f"解密失败: {str(e)}")
        return ""

# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    test_text = "测试加密文本123456"
    print(f"原始文本: {test_text}")
    
    # 加密测试
    encrypted = sm4_encrypt(test_text)
    print(f"加密结果: {encrypted}")
    
    # 解密测试
    decrypted = sm4_decrypt(encrypted)
    print(f"解密结果: {decrypted}")
    
    # 验证结果
    if decrypted == test_text:
        print("测试成功: 加密解密一致")
    else:
        print("测试失败: 加密解密不一致") 