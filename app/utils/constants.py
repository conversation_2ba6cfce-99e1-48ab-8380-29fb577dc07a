from enum import Enum


class ProductType(str, Enum):
    """产品类型枚举"""
    INSIGHTPLUS = "INSIGHTPLUS"      # 灵感发现
    DOCGEN = "DOCGEN"                # 材料生成
    LABPLAN = "LABPLAN"              # 实验计划
    OPTITECH = "OPTITECH"            # 设备耗材
    DATADRIVER = "DATADRIVER"        # 实验数据分析
    COWRITER = "COWRITER"            # 论文撰写
    ANALYZER = "ANALYZER"            # 项目智评


class ProductTypeText(str, Enum):
    """产品类型文本描述"""
    INSIGHTPLUS = "灵感发现"
    DOCGEN = "材料生成"
    LABPLAN = "实验计划"
    OPTITECH = "设备耗材"
    DATADRIVER = "实验数据分析"
    COWRITER = "论文撰写"
    ANALYZER = "项目智评" 