from fastapi import Request
from fastapi.exceptions import RequestValidationError, HTTPException
from fastapi.responses import JSONResponse
# from fastapi.exception_handlers import request_validation_exception_handler
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY, HTTP_401_UNAUTHORIZED, HTTP_500_INTERNAL_SERVER_ERROR
# from app.utils.utils import send_data

async def custom_validation_exception_handler(request: Request, exc: RequestValidationError):
  errorMsg = ""
  try:  
    error = exc.errors()
    for item in error:
      type = item.get("type")
      if type == 'missing':
        try:
          field = item.get("loc")[1]
          errorMsg += f"，缺少{field}字段" if errorMsg else f"缺少{field}字段"
        except:
          errorMsg = "请求体body里面缺少参数"
      elif type == 'value_error':
        field = item.get("ctx").get("error")
        errorMsg += f"，{field}" if errorMsg else f"{field}"
      elif type in ['extra_forbidden', 'uuid_parsing']:
        field = item.get("loc")[1]
        msg = item.get("msg")
        errorMsg += f"，【{field}】{msg}" if errorMsg else f"【{field}】{msg}"
      else:
        msg = item.get("msg")
        errorMsg += f"，{msg}" if errorMsg else f"{msg}"
  except:
    errorMsg += "未知的验证错误"
  return JSONResponse(
    status_code=HTTP_422_UNPROCESSABLE_ENTITY,
    content = {
      "code": 422,
      "error": errorMsg,
      "data": None,
      "success": False
    })
async def custom_no_auth_handler(request: Request, exc: HTTPException):
  status = exc.status_code
  if status == 401:
    return JSONResponse(
      status_code=HTTP_401_UNAUTHORIZED,
      content = {
        "code": 401,
        "error": "无效的认证凭据",
        "data": None,
        "success": False
      })
  elif status == 403:
    return JSONResponse(
      status_code=403,
      content = {
        "code": 403,
        "error": exc.detail,
        "data": None,
        "success": False
      })
  else:
    return JSONResponse(
      status_code=HTTP_500_INTERNAL_SERVER_ERROR,
      content = {
        "code": 500,
        "error": exc.detail,
        "data": None,
        "success": False
      })