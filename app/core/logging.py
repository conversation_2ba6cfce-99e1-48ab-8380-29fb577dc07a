import os
import sys
import threading
from pathlib import Path
from datetime import datetime
from loguru import logger
from app.core.config import settings

# 线程锁，确保日志配置只初始化一次
_logger_initialized = False
_lock = threading.Lock()

def init_logger():
    """
    初始化 loguru 日志系统
    - 按天创建目录 (YYYY-MM-DD)
    - 按小时创建日志文件 (YYYY-MM-DD_HH.log)
    - 包含详细的时间戳格式
    - 使用东八区时区
    """
    global _logger_initialized
    
    with _lock:
        if _logger_initialized:
            return
        
        # 设置时区为东八区，确保日志时间正确
        os.environ['TZ'] = settings.TZ
        if hasattr(os, 'tzset'):
            os.tzset()
            
        # 移除默认的 logger
        logger.remove()
        
        # 确保日志目录存在
        log_dir = Path(settings.LOG_DIR)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 自定义日志格式，类似Spring Boot风格
        log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> "
            "<level>{level: <8}</level> "
            "<cyan>{process}</cyan> --- "
            "[<yellow>{thread}</yellow>] "
            "<blue>{name}</blue> : {message}"
        )
        
        # 控制台输出 (开发环境可见)
        logger.add(
            sys.stdout,
            format=log_format,
            level=settings.LOG_LEVEL.upper(),
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # 文件输出 - 按天按小时分割
        # 使用自定义的文件路径格式: logs/YYYY-MM-DD/YYYY-MM-DD_HH.log
        log_file_pattern = str(log_dir / "{time:YYYY-MM-DD}" / "{time:YYYY-MM-DD_HH}.log")
        
        logger.add(
            log_file_pattern,
            format=(
                "{time:YYYY-MM-DD HH:mm:ss.SSS} "
                "{level: <8} "
                "{process} --- "
                "[{thread}] "
                "{name} : {message}"
            ),
            level=settings.LOG_LEVEL.upper(),
            rotation="1 hour",  # 每小时轮转
            retention=f"{settings.LOG_RETENTION_DAYS} days",  # 使用配置的保留天数
            compression="zip",  # 压缩旧日志
            backtrace=True,
            diagnose=True,
            enqueue=True,  # 异步写入，提高性能
            serialize=False  # 文本格式，非JSON
        )
        
        _logger_initialized = True
        logger.info(f"日志系统初始化完成，日志目录: {log_dir}")
        logger.info(f"日志级别: {settings.LOG_LEVEL.upper()}")
        logger.info(f"日志保留天数: {settings.LOG_RETENTION_DAYS}天")


class LoggerAdapter:
    """
    适配器类，提供与标准 logging 兼容的接口
    这样现有代码的 logger.info(), logger.error() 等调用不需要修改
    """
    
    def __init__(self, name: str):
        self.name = name
        # 确保日志系统已初始化
        init_logger()
    
    def debug(self, message, *args, **kwargs):
        logger.opt(depth=1).debug(f"[{self.name}] {message}", *args, **kwargs)
    
    def info(self, message, *args, **kwargs):
        logger.opt(depth=1).info(f"[{self.name}] {message}", *args, **kwargs)
    
    def warning(self, message, *args, **kwargs):
        logger.opt(depth=1).warning(f"[{self.name}] {message}", *args, **kwargs)
    
    def warn(self, message, *args, **kwargs):
        # 兼容旧的 warn 方法
        self.warning(message, *args, **kwargs)
    
    def error(self, message, *args, **kwargs):
        logger.opt(depth=1).error(f"[{self.name}] {message}", *args, **kwargs)
    
    def critical(self, message, *args, **kwargs):
        logger.opt(depth=1).critical(f"[{self.name}] {message}", *args, **kwargs)
    
    def exception(self, message, *args, **kwargs):
        logger.opt(depth=1, exception=True).error(f"[{self.name}] {message}", *args, **kwargs)


def get_logger(name: str) -> LoggerAdapter:
    """
    获取指定名称的logger实例，兼容现有代码接口
    
    Args:
        name: logger名称，通常为模块名称 (__name__)
        
    Returns:
        LoggerAdapter: 配置好的logger适配器实例
    """
    return LoggerAdapter(name)


# 在模块加载时初始化日志系统
init_logger() 