from app.models.user_default_model import UserDefaultModels
from app.models.model_config import ModelConfig
from typing import List
from app.core.logging import get_logger
from datetime import datetime
from app.api.schemas.user import UserResponse
from app.api.repository.organization_model import get_org_default_model
from app.api.schemas.user_default_model import UserDefaultModelCreate, UserDefaultUseResponse
from app.api.schemas.role import InsetRole
from app.api.schemas.model_config import ModelConfigBase
from app.models.organization_model_use import UseCase
from app.utils.enum import UserDefaultModelError

logger = get_logger(__name__)

async def get_user_model(
  # 这是当前登录用户信息
  current_user: UserResponse,
  # 默认使用场景
  use_case: str
) -> ModelConfigBase:
    """
    根据用户ID和某个场景的查询默认使用模型
    
    Args:
        current_user: 用户信息
        use_case: UseCase使用场景
    Returns:
        UserDefaultModels: 用户的默认模型
    """
    try:
        if not current_user:
            raise Exception(UserDefaultModelError.USER_IS_EMPTY.value)
        if not use_case:
            raise Exception(UserDefaultModelError.CASE_IS_EMPTY.value)
        if use_case not in [item.value for item in UseCase]:
            raise Exception(UserDefaultModelError.CASE_IS_INVALID.value)
        model = await UserDefaultModels.filter(
            user_id=current_user.id,
            is_deleted=False,
            default_way=use_case
        ).prefetch_related("model").first()
        if not model:
            if current_user.role.identifier == InsetRole.SUPER_ADMIN or not current_user.organization:
                model_info = await ModelConfig.filter(
                    is_deleted=False
                ).first()
                model = ModelConfigBase.model_validate(model_info, from_attributes=True)
            else:
                temp = await get_org_default_model(
                    organization_id=current_user.organization.id,
                    use = use_case
                )
                
                model = ModelConfigBase.model_validate(temp.model, from_attributes=True)
        else:
            model = ModelConfigBase.model_validate(model.model, from_attributes=True)
        return model
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        raise Exception(error_msg)

async def get_user_default_models(
  # 这是当前登录用户信息
  current_user: UserResponse
) -> List[UserDefaultUseResponse]:
    """
    根据用户ID查询该用户的默认模型
    
    Args:
        current_user: 用户信息
        
    Returns:
        List[UserDefaultModels]: 用户的默认模型列表
    """
    try:
        way_list = [item.value for item in UseCase]
        result: List[UserDefaultUseResponse] = []
        models = await UserDefaultModels.filter(
            user_id=current_user.id,
            is_deleted=False
        ).prefetch_related("model").all()
        # 将没有设置默认模型的场景筛选出来
        for model in models:
            if model.default_way in way_list:
                result.append(UserDefaultUseResponse.model_validate(model, from_attributes=True))
                way_list = [item for item in way_list if item != model.default_way]
        # 如果有场景没有设置默认模型，则读取机构的该场景的默认模型
        if way_list:
            for use in way_list:
                # 如果是超级管理员或者不属于任何机构的用户
                if current_user.role.identifier == InsetRole.SUPER_ADMIN.value or not current_user.organization:
                    # 模型列表的第一个作为该场景的默认模型
                    model = await ModelConfig().filter(
                        is_deleted=False
                    ).first()
                    model_info = ModelConfigBase.model_validate(model, from_attributes=True).model_dump()
                    result.append(UserDefaultUseResponse.model_validate({
                        "model": model_info,
                        "default_way": use
                    }, from_attributes=True))
                # 如果是机构用户就读取组织的默认模型作为场景的默认模型
                elif current_user.organization:
                    org_model = await get_org_default_model(
                        organization_id=current_user.organization.id,
                        use=use
                    )
                    result.append(UserDefaultUseResponse.model_validate(org_model.model_dump(), from_attributes=True))

        return result
    except Exception as e:
        error_msg = str(e)
        logger.error(error_msg)
        raise Exception(error_msg)

async def create_user_default_models(
    current_user: UserResponse,
    data: UserDefaultModelCreate
) -> bool:
    """
    根据用户ID和模型列表插入数据
    
    Args:
        current_user: 用户信息
        model_list: 模型列表，每个元素包含 model_id 和 default_way
        
    Returns:
        bool: 操作是否成功
    """
    try:
        # 然后设置为新的默认
        for item in data.list_model:
            # 先将模型删除
            await UserDefaultModels.filter(
                user_id=current_user.id,
                is_deleted=False,
                default_way=item.default_way
            ).update(
                is_deleted=True,
                deleted_at=datetime.now()
            )
            await UserDefaultModels.create(
                user_id=current_user.id,
                model_id=item.model_id,
                default_way=item.default_way
            )
        return True
    except Exception as e:
        logger.error(f"创建用户默认模型失败: {str(e)}")
        raise Exception(f"创建用户默认模型失败: {str(e)}")
