from app.models.user import User
from app.models.user_report_usage import UserReportUsage
from app.models.organizations import Organizations
from app.api.schemas.role import InsetRole
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.api.repository.organizations import calculate_organization_left_usable_count
from app.api.schemas.user_report_usage import (
    UserReportUsageCreate,
    UserReportUsageResponse,
)
from typing import Optional

logger = get_logger(__name__)


async def create_user_report_usage(
    user_report_usage: UserReportUsageCreate
):
    """创建和修改用户报告最大可用生成次数"""
    # 验证用户存在
    user_result = None
    if user_report_usage.user_id:
      user_result = await User.filter(id=user_report_usage.user_id, is_deleted=False).prefetch_related("role", "organization").first()
      if not user_result:
        raise Exception("用户不存在")
    user = UserResponse.model_validate(user_result)
    # 检查是否已有记录
    existing = await UserReportUsage.get_or_none(user_id_id=user_report_usage.user_id, is_deleted=False)
    if existing:
      # 检查用户角色并设置适当的值
      if user.role.identifier in [InsetRole.ADMIN, InsetRole.SUPER_ADMIN]:
        # 管理员和超级管理员设置为null
        existing.max_allowed_count = None
      else:
        # 普通用户设置为指定值
        if existing.used_count > user_report_usage.max_allowed_count:
           raise Exception(f"使用次数已经已经超过了当前设置的最大使用次数")
        if user_result.organization:
            org_left_count = await calculate_organization_left_usable_count(
               organization_id= user.organization.id
            )
            logger.info(f"机构剩余的可分配次数：{org_left_count}")
            # 回收已经分配的数量
            org_left_count += (existing.max_allowed_count or 0)
            if (org_left_count < user_report_usage.max_allowed_count):
               raise Exception(f"给用户分配的最大可用次数已经超过组织的剩余可分配次数：{org_left_count}")
        existing.max_allowed_count = user_report_usage.max_allowed_count
      # existing.used_count = 10
      # 确保更改保存到数据库
      await existing.save()
      await existing.refresh_from_db()
      logger.info(f"数据库里面max_allowed_count={existing.max_allowed_count}")
      
      # 将关联对象转换为字典，确保user_id是字符串格式
      existing_data = {
          "id": str(existing.id),
          "user_id": str(existing.user_id_id),
          "used_count": existing.used_count,
          "max_allowed_count": existing.max_allowed_count,
          "created_at": existing.created_at,
          "updated_at": existing.updated_at,
          "is_deleted": existing.is_deleted
      }
      
      result = UserReportUsageResponse.model_validate(existing_data)
      return result
    else: 
        if user_result.organization:
            org_left_count = await calculate_organization_left_usable_count(
               organization_id= user.organization.id
            )
            logger.info(f"org_left_count:{org_left_count}, max_allowed_count: {user_report_usage.max_allowed_count}")
            if (org_left_count < user_report_usage.max_allowed_count):
               raise Exception(f"给用户分配的最大可用次数已经超过组织的剩余可分配次数：{org_left_count}")
    # 创建新记录
        new_usage = await UserReportUsage.create(
            user_id_id=user_report_usage.user_id,
            used_count=0,
            max_allowed_count= None if user.role.identifier in [InsetRole.ADMIN, InsetRole.SUPER_ADMIN] else user_report_usage.max_allowed_count
        )
      
        # 将关联对象转换为字典，确保user_id是字符串格式
        new_usage_data = {
            "id": str(new_usage.id),
            "user_id": str(new_usage.user_id_id),
            "used_count": new_usage.used_count,
            "max_allowed_count": new_usage.max_allowed_count,
            "created_at": new_usage.created_at,
            "updated_at": new_usage.updated_at,
            "is_deleted": new_usage.is_deleted
        }
        
        result = UserReportUsageResponse.model_validate(new_usage_data)
        return result

async def get_count_data(
    user_id: str,
    is_admin: bool,
    organization_id: Optional[str]
):
   result = await UserReportUsage.filter(user_id_id=user_id).first()
   left_count = 0
   if is_admin and organization_id and result:
        left_count = await calculate_organization_left_usable_count(
            organization_id=organization_id
        )
        result.max_allowed_count = left_count
   return result
async def check_user_usage_limit(
  user_id: str
) -> bool:
    """
    检查用户的使用次数限制
    
    Args:
        user: 用户对象（需要预加载role和organization）
    
    Returns:
      True 表示还有可用次数

    Exception:
      表示有错误
    """
    try:
        # 获取用户的使用记录
        user_report_usage = await UserReportUsage.filter(
          user_id_id=user_id,
          is_deleted=False
        ).first()
        if not user_report_usage:
            raise Exception("用户报告使用次数记录不存在")
        user_result = await User.filter(id=user_id, is_deleted=False).prefetch_related("organization", "role").first()
        if not user_result:
            raise Exception("用户不存在")
        user = UserResponse.model_validate(user_result, from_attributes=True)
        # 优先根据角色判断，而不是依赖数据字段
        if user.role.identifier == InsetRole.SUPER_ADMIN:
            # 超级管理员：无限制
            return True
        elif user.role.identifier == InsetRole.ADMIN:
            logger.info(f"机构管理员 {user.username} 使用次数检查开始")
            
            # 获取机构信息
            if not user.organization:
              raise Exception("用户未绑定机构")
            organization = await Organizations.filter(id=user.organization.id, is_deleted=False).first()
            if not organization:
                raise Exception("用户所属机构不存在")
            
            organization_total_count = organization.limit_count or 0
            logger.info(f"机构 {organization.name} 总次数: {organization_total_count}")
            
            # 计算机构内所有用户已分配的次数总和（排除管理员）
            remaining_unallocated = await calculate_organization_left_usable_count(
                organization_id=user.organization.id
            )
            # 计算管理员真正可用的次数
            available_for_admin = remaining_unallocated - user_report_usage.used_count
            if available_for_admin <= 0:
                raise Exception(f"机构剩余未分配次数不足（剩余{remaining_unallocated}次，您已使用{user_report_usage.used_count}次）")
            
            return True
        else:
            # 普通用户：检查个人配额
            if user_report_usage.max_allowed_count is None:
                # 数据异常：普通用户却没有配额限制，应该设为0
                logger.warning(f"普通用户 {user.username} 的配额为None，设置为0")
                user_report_usage.max_allowed_count = 0
                await user_report_usage.save()
            logger.info(f"已经使用：{user_report_usage.used_count}，总共可使用{user_report_usage.max_allowed_count}")
            if user_report_usage.used_count >= user_report_usage.max_allowed_count:
                raise Exception(f"您已达到最大使用次数限制({user_report_usage.max_allowed_count}次)")
            
            # remaining = user_report_usage.max_allowed_count - user_report_usage.used_count
            return True
            
    except Exception as e:
        logger.error(f"检查用户使用次数限制时发生错误: {str(e)}")
        raise Exception(f"{str(e)}")