from app.models.organization_model import OrganizationModels
from app.api.schemas.organization_model import (
    OrganizationModelCreate,
    OrganizationModelWithUse,
    OrganizationDefaultUse,
    OrganizationDefaultUseResponse
)
from app.models.model_config import ModelConfig
from typing import List
from pydantic import UUID4
from app.core.logging import get_logger
from app.utils.enum import (
  OrganizationModelError
)
from datetime import datetime
from app.models.organization_model_use import OrganizationModelUses, UseCase
from app.models.organizations import Organizations


logger = get_logger(__name__)

# 获取某个模型模型是否被分配给机构
async def validate_model_distributed(model_id: UUID4) -> bool:
  result = await OrganizationModels.filter(
    is_deleted=False,
    model_id=model_id
  ).first()
  return result is not None
# 根据机构的最新可用模型，修改机构的各个场景的默认模型
async def change_default_base_on_authed_model(
  data: OrganizationModelCreate
):
  existed_list = await OrganizationModelUses.filter(
    organization_id=data.organization_id,
    is_deleted=False
  )
  need_set = [item.value for item in UseCase]
  for default_info in existed_list:
    model_id = default_info.model_id
    # 如果默认场景使用的模型不在本次更新的模型列表里面
    if model_id not in data.list_model:
      default_info.model_id = data.list_model[0]
      await default_info.save()
    need_set = [item for item in need_set if item != default_info.default_way]
  if need_set:
    for item in need_set:
      await OrganizationModelUses.create(
        organization_id=data.organization_id,
        model_id=data.list_model[0],
        default_way=item
      )
  return True
# 为机构分配可用的模型
async def create_organization_model(
  data: OrganizationModelCreate
) -> bool:
  # 查询组织的所有模型(包含软删除的)
  org_model_list = await OrganizationModels.filter(
     organization_id=data.organization_id
  ).all()
  # 遍历所有的模型列表
  count = 0
  for model_id in data.list_model:
    model_data = await ModelConfig.filter(
      is_deleted=False,
      id=model_id
    ).first()
    related_exist = next((item for item in org_model_list if item.model_id == model_id), None)
    # 如果模型存在
    if model_data:
      # 更新列表信息，移除已经找到模型
      org_model_list = [item for item in org_model_list if item.model_id != model_id]  
      # 如果组织和机构关联表里面有这条关联数据则重新将该条记录放出来。
      if related_exist:
        related_exist.is_deleted = False
        related_exist.deleted_at = None
        await related_exist.save()
      # 否则重新创建
      else:
        await OrganizationModels.create(
          organization_id=data.organization_id,
          model_id=model_id,
        )
      count += 1
    else:
      error_msg = OrganizationModelError.NOT_MODEL_RECORD.value + model_id
      logger.error(error_msg)
      raise Exception(error_msg)
  # 剩下没有被匹配的数据全部删除
  if org_model_list:
    for item in org_model_list:
      await OrganizationModels.filter(model_id=item.model_id).update(
        is_deleted=True,
        deleted_at=datetime.now()
      )
  await change_default_base_on_authed_model(data)
  return True
async def get_organization_model(
  organization_id: UUID4
) -> List[OrganizationModelWithUse]:
  """
    获取机构的所有可用模型
  """
  use_way_list = await OrganizationModelUses.filter(
    is_deleted=False,
    organization_id=organization_id
  ).all()
  # 这里用来维护各个用途的默认模型
  use_map: dict[str, List[str]] = {}
  for item in use_way_list:
    if item.model_id in use_map:
      use_map[item.model_id].append(item.default_way)
    else:
      use_map[item.model_id] = [item.default_way]
  # 查询所有的机构模型
  org_all_model = await OrganizationModels.filter(
    organization_id=organization_id,
    is_deleted=False
  ).prefetch_related("model").all()
  result: List[OrganizationModelWithUse] = []
  # 遍历所有模型给赋default_way
  for content in org_all_model:
    item = OrganizationModelWithUse.model_validate(content, from_attributes=True)
    if item.model.id in use_map:
      item.default_way = use_map[item.model.id]
    result.append(item)
  return result
# 获取机构的所有场景的默认模型列表
async def get_default_use(
  organization_id: UUID4
) -> List[OrganizationDefaultUseResponse]:
  list_data = await OrganizationModelUses.filter(
    organization_id=organization_id,
    is_deleted=False
  ).prefetch_related("model").all()
  return [OrganizationDefaultUseResponse.model_validate(item, from_attributes=True) for item in list_data]
# 获取机构指定场景的默认模型
async def get_org_default_model(
  organization_id: UUID4,
  use: str
) -> OrganizationDefaultUseResponse:
  data = await OrganizationModelUses.filter(
    organization_id=organization_id,
    default_way=use,
    is_deleted=False
  ).prefetch_related("model").first()
  if not data:
    data = await OrganizationModels.filter(
      organization_id=organization_id,
      is_deleted=False,
    ).prefetch_related("model").first()
    if not data:
      raise Exception(OrganizationModelError.ORG_NO_MODEL.value)
    data = OrganizationDefaultUseResponse.model_validate(data, from_attributes=True)
    data.default_way = use
    return data
  else:
    return OrganizationDefaultUseResponse.model_validate(data, from_attributes=True)
# 修改机构的各个场景的默认模型
async def change_default_use(
  data: OrganizationDefaultUse,
  organization_id: UUID4
) -> bool:
  """
    给机构设定各个场景默认使用的模型
  """
  # 然后设置为新的默认
  for item in data.list:
    # 先将模型删除
    await OrganizationModelUses.filter(
      organization_id=organization_id,
      is_deleted=False,
      default_way=item.default_way
    ).update(
      is_deleted=True,
      deleted_at=datetime.now()
    )
    await OrganizationModelUses.create(
      organization_id=organization_id,
      model_id=item.model_id,
      default_way=item.default_way
    )
  return True
