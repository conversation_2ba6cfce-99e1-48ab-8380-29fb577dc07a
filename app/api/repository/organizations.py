from app.models.organizations import Organizations
from app.models.user_report_usage import UserReportUsage
from app.core.logging import get_logger

logger = get_logger(__name__)
async def calculate_organization_left_usable_count(
  organization_id: str
):
  ### 利用机构的可使用次数总数减去已经分配给用户的额度得到剩余可使用的次数 ###
  try:
    organization = await Organizations.filter(
      id=organization_id
    ).first()
    # 计算机构内所有用户已分配的次数总和（排除管理员，因为管理员的max_allowed_count是null）
    result = await UserReportUsage.filter(
        user_id__organization__id=organization_id,
        user_id__is_deleted=False,
        is_deleted=False,
        max_allowed_count__not_isnull=True  # 排除管理员（max_allowed_count为null的用户）
    ).all()
    distributed_total = sum(item.max_allowed_count for item in result if item.max_allowed_count is not None)
    logger.info(f"已经分配的数量：{distributed_total}")
    total_allocated = distributed_total if distributed_total else 0
    return (organization.limit_count or 0) - (total_allocated or 0)
  except Exception as e:
    logger.info(f"calculate_organization_left_usable_count函数报错：{str(e)}")
    raise Exception(str(e))