from app.models.llm_call_log import LlmCallLog
from app.api.schemas.user import UserResponse
from typing import Optional
from pydantic import UUID4

# 这是创建大模型调用日志
async def create_llm_call_log(
  current_user: UserResponse,
  model_name: str,
  model_api_key: str,
  model_api_url: str,
  response: str,
  messages: any,
  product_type: str,
  related_id: Optional[UUID4] = None,
  flag: Optional[str] = ""
):
  try:
    result = await LlmCallLog.create(
      model_name=model_name,
      model_api_url=model_api_url,
      model_api_key=model_api_key,
      prompt=messages,
      response=response,
      description=flag,
      user_id=current_user.id,
      product_type=product_type,
      related_id=related_id
    )
    return result
  except Exception as e:
    raise e
# 更新大模型调用的日志。因为response可能时间很长，所以需要更新接口
async def update_llm_call(
  # 大模型返回的完整信息
  response: str,
  log_id: UUID4
):
  try:
    await LlmCallLog.filter(
      id=log_id
    ).update(
      response=response
    )
    return True
  except Exception as e:
    raise e
