from app.api.schemas.role import InsetRole
from app.api.schemas.user import UserResponse
from typing import Optional

def is_authorization(
  user: UserResponse,
  resource_user: Optional[UserResponse]
):
  # 如果资源没有用户属性
  if not resource_user:
    return True    
  # 如果是超级管理员，就都有权限
  elif user.role.identifier == InsetRole.SUPER_ADMIN.value:
    return True
  elif user.role.identifier == InsetRole.ADMIN.value:
    # 如果是管理员但是没有组织属性，则当成一般成员处理
    if not user.organization:
      return user.id == resource_user.id
    # 如果是管理员，资源的用户属性有组织，且两者的组织id一致则机构管理员有权限
    else:
      return user.organization.id == resource_user.organization.id if resource_user.organization else user.id == resource_user.id
  # 如果是普通用户
  else:
    return user.id == resource_user.id