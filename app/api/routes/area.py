from fastapi import APIRouter, Query
from typing import List, Optional
from app.models.area import Area
from app.api.schemas.area import AreaResponse
from app.utils.utils import send_data, ResponseModel

router = APIRouter()

@router.get("/children", response_model=ResponseModel[List[AreaResponse]])
async def get_area_children(
    area_id: Optional[int] = Query(None, description="父级区域ID，不传则返回所有省份")
):
    """获取指定区域的子级区域列表"""
    query = Area.filter(is_deleted=False)
    if area_id is not None:
        query = query.filter(parent_id=area_id)
    else:
        # 如果area_id为空，则返回所有省份（parent_id为空的记录）
        query = query.filter(parent_id=None)
    
    areas = await query.all()
    data = [AreaResponse.model_validate(area, from_attributes=True) for area in areas]
    return send_data(True, data) 