from fastapi import APIRouter, status, Depends, Query, Request
from typing import List, Optional
from uuid import UUID
from datetime import datetime

from app.models.menu import Menu
from app.api.schemas.menu import MenuCreate, MenuUpdate, MenuResponse, MenuTreeResponse
from app.utils.utils import send_data, ResponseModel
from app.api.deps import get_current_user_from_state
from app.api.schemas.user import UserResponse
from app.api.schemas.role import InsetRole

router = APIRouter()

@router.post("", response_model=ResponseModel[MenuResponse], status_code=status.HTTP_201_CREATED)
async def create_menu(
    menu: MenuCreate,
    request: Request
):
    """创建新菜单"""
    current_user = get_current_user_from_state(request)
    # 只有超级管理员才可以创建菜单
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    try:
        # 检查标识符是否已存在
        existing_menu = await Menu.filter(identifier=menu.identifier, is_deleted=False).first()
        if existing_menu:
            return send_data(False, None, "菜单标识符已存在")

        # 如果指定了父菜单，检查父菜单是否存在
        if menu.parent_id:
            parent_menu = await Menu.filter(id=menu.parent_id, is_deleted=False).first()
            if not parent_menu:
                return send_data(False, None, "父菜单不存在")

        # 如果没有指定order，则设置为当前机构下最大order值加1
        order = menu.order
        if order is None:
            max_order_menu = await Menu.filter(
                is_deleted=False
            ).order_by("-order").first()
            order = (max_order_menu.order + 1) if max_order_menu else 0
        db_menu = await Menu.create(
            name=menu.name,
            identifier=menu.identifier,
            order=order,
            parent_id=menu.parent_id
        )
        return send_data(True, MenuResponse.model_validate(db_menu, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"创建菜单失败: {str(e)}")

@router.get("/tree", response_model=ResponseModel[List[MenuTreeResponse]])
async def get_menu_tree(
    request: Request
):
    """获取菜单树形结构"""
    # 只有超级管理员才可以创建菜单
    current_user = get_current_user_from_state(request)
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    try:
        query = Menu.filter(is_deleted=False)
        menus = await query.order_by("-order", "-updated_at").all()
        
        # 构建菜单树
        menu_dict = {}
        root_menus = []
        
        # 先将所有菜单放入字典
        for menu in menus:
            menu_dict[menu.id] = MenuTreeResponse.model_validate(menu, from_attributes=True)
            menu_dict[menu.id].children = []
        
        # 构建树形结构
        for menu in menus:
            menu_response = menu_dict[menu.id]
            if menu.parent_id is None:
                root_menus.append(menu_response)
            else:
                if menu.parent_id in menu_dict:
                    menu_dict[menu.parent_id].children.append(menu_response)
        
        return send_data(True, root_menus)
    except Exception as e:
        return send_data(False, None, f"获取菜单树失败: {str(e)}")

@router.get("/{menu_id}", response_model=ResponseModel[MenuResponse])
async def get_menu(
    menu_id: UUID,
    request: Request
):
    """获取特定菜单详情"""
    current_user = get_current_user_from_state(request)
    # 只有超级管理员才可以编辑菜单
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    try:
        menu = await Menu.filter(
            id=menu_id,
            is_deleted=False
        ).first()
        if not menu:
            return send_data(False, None, "菜单不存在")
        return send_data(True, MenuResponse.model_validate(menu, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"获取菜单详情失败: {str(e)}")

@router.put("/{menu_id}", response_model=ResponseModel[MenuResponse])
async def update_menu(
    menu_id: UUID,
    menu: MenuUpdate,
    request: Request
):
    """更新菜单信息"""
    current_user = get_current_user_from_state(request)
    # 只有超级管理员才可以编辑菜单
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    try:
        db_menu = await Menu.filter(id=menu_id, is_deleted=False).first()
        if not db_menu:
            return send_data(False, None, "菜单不存在")
        # db = MenuResponse.model_validate(db_menu)
        # 如果更新标识符，检查是否已存在
        if menu.identifier and menu.identifier != db_menu.identifier:
            existing_menu = await Menu.filter(
                identifier=menu.identifier,
                is_deleted=False
            ).first()
            if existing_menu:
                return send_data(False, None, "菜单标识符已存在")

        # 如果更新父菜单ID，检查父菜单是否存在
        if menu.parent_id is not None:
            if menu.parent_id:
                parent_menu = await Menu.filter(
                    id=menu.parent_id,
                    is_deleted=False,
                ).first()
                if not parent_menu:
                    return send_data(False, None, "父菜单不存在")
            db_menu.parent_id = menu.parent_id

        update_data = menu.model_dump(exclude_unset=True, exclude={"module_id", "parent_id"})
        await db_menu.update_from_dict(update_data)
        await db_menu.save()
        
        # 重新获取更新后的菜单（包含关联数据）
        # updated_menu = await Menu.filter(id=menu_id).first()
        return send_data(True, MenuResponse.model_validate(db_menu, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新菜单失败: {str(e)}")

@router.delete("/{menu_id}", response_model=ResponseModel[MenuResponse])
async def delete_menu(
    menu_id: UUID,
    request: Request,
    hard_delete: bool = Query(False, description="是否物理删除")
):
    """删除菜单（软删除）"""
    current_user = get_current_user_from_state(request)
    # 只有超级管理员才可以删除菜单
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    try:
        menu = Menu.filter(
            id=menu_id,
            is_deleted=False
        )
        menu = await menu.first()
        if not menu:
            return send_data(False, None, "菜单不存在")

        # 检查是否有子菜单
        has_children = await Menu.filter(parent_id=menu_id, is_deleted=False).exists()
        if has_children:
            return send_data(False, None, "该菜单下存在子菜单，无法删除")
        if hard_delete:
            await menu.delete()
        else:
            menu.is_deleted = True
            menu.deleted_at = datetime.now()
            await menu.save()
        
        # 重新获取删除后的菜单（包含关联数据）
        # deleted_menu = await Menu.filter(id=menu_id).first()
        return send_data(True, MenuResponse.model_validate(menu, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除菜单失败: {str(e)}")
    