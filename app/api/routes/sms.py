from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, HTTPException, status
from typing import Optional
import json
import uuid

from app.core.config import settings
from app.core.security import create_access_token, get_password_hash
from app.models.user import User
from app.models.organizations import Organizations
from app.models.role import Role
from app.api.schemas.sms import (
    SMSCodeRequest, 
    SMSCodeResponse,
    MobileLoginRequest,
    MobileLoginResponse
)
from app.services.sms_service import sms_service
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.core.redis_client import get_redis_client

router = APIRouter()
logger = get_logger(__name__)


@router.post("/send-code", response_model=ResponseModel[SMSCodeResponse])
async def send_sms_code(request: SMSCodeRequest):
    """发送短信验证码"""
    try:
        logger.info(f"发送短信验证码请求: {request.mobile}")
        
        # 调用短信服务发送验证码
        result = await sms_service.send_verification_code(request.mobile)
        
        response_data = SMSCodeResponse(
            success=result["success"],
            message=result["message"],
            request_id=result.get("request_id")
        )
        
        if result["success"]:
            logger.info(f"短信验证码发送成功: {request.mobile}")
            return send_data(True, response_data, "验证码发送成功")
        else:
            logger.warning(f"短信验证码发送失败: {request.mobile}, 原因: {result['message']}")
            return send_data(False, response_data, result["message"])
            
    except Exception as e:
        logger.error(f"发送短信验证码异常: {request.mobile}, 错误: {str(e)}")
        return send_data(False, None, "验证码发送失败，请稍后重试")


@router.post("/mobile-login", response_model=ResponseModel[MobileLoginResponse])
async def mobile_login(request: MobileLoginRequest):
    """手机号验证码登录/注册"""
    try:
        logger.info(f"手机号验证码登录请求: {request.mobile}")
        
        # 验证短信验证码
        is_valid = await sms_service.verify_code(request.mobile, request.code)
        if not is_valid:
            logger.warning(f"验证码验证失败: {request.mobile}")
            return send_data(False, None, "验证码错误或已过期")
        
        # 查找用户是否已存在
        user = await User.filter(
            mobile=request.mobile, 
            is_deleted=False
        ).prefetch_related('role', 'organization').first()
        
        is_new_user = False
        
        if not user:
            # 用户不存在，创建新用户
            is_new_user = True
            logger.info(f"创建新用户: {request.mobile}")
            
            # 获取默认组织（如果没有则创建一个默认组织）
            default_org = await Organizations.filter(is_deleted=False).first()
            if not default_org:
                # 创建默认组织
                default_org = await Organizations.create(
                    id=uuid.uuid4(),
                    name="默认组织",
                    description="系统默认组织",
                    is_deleted=False
                )
                logger.info(f"创建默认组织: {default_org.id}")
            
            # 获取默认角色（普通用户角色）
            default_role = await Role.filter(name="user", is_deleted=False).first()
            if not default_role:
                # 创建默认用户角色
                default_role = await Role.create(
                    id=uuid.uuid4(),
                    name="user",
                    description="普通用户",
                    is_deleted=False
                )
                logger.info(f"创建默认用户角色: {default_role.id}")
            
            # 创建新用户
            user = await User.create(
                id=uuid.uuid4(),
                username=f"mobile_{request.mobile}",  # 使用手机号生成用户名
                mobile=request.mobile,
                hashed_password=get_password_hash(""),  # 手机号登录不需要密码，设置空密码
                organization_id=default_org.id,
                role_id=default_role.id,
                is_deleted=False
            )
            
            # 预加载关联对象
            await user.fetch_related('role', 'organization')
            
            logger.info(f"新用户创建成功: {user.id}")
        else:
            logger.info(f"用户已存在，直接登录: {user.id}")
        
        # 生成JWT token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=json.dumps({
                "username": user.username,
                "user_id": str(user.id),
                "mobile": user.mobile
            }), 
            expires_delta=access_token_expires
        )
        
        # 单点登录处理（如果开启）
        if settings.IS_OPEN_SSO == 'ON':
            redis = get_redis_client()
            redis_key = f"sso:{user.username}"
            if await redis.get(redis_key):
                await redis.delete(redis_key)
            await redis.set(
                redis_key,
                access_token,
                ex=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        # 构建响应数据
        response_data = MobileLoginResponse(
            access_token=access_token,
            token_type="bearer",
            user_id=str(user.id),
            username=user.username,
            mobile=user.mobile,
            organization_id=str(user.organization.id) if user.organization else "",
            is_new_user=is_new_user
        )
        
        logger.info(f"手机号登录成功: {user.username}, 新用户: {is_new_user}")
        return send_data(
            True, 
            response_data, 
            "登录成功" if not is_new_user else "注册并登录成功"
        )
        
    except Exception as e:
        logger.error(f"手机号登录异常: {request.mobile}, 错误: {str(e)}")
        return send_data(False, None, "登录失败，请稍后重试")