from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, HTTPException, status
from typing import Optional
import json
import uuid
from app.core.config import settings
from app.core.security import (
    create_access_token,
    get_password_hash,
    generate_complex_password,
)
from app.models.user import User
from app.models.organizations import Organizations
from app.models.role import Role
from app.api.schemas.sms import SMSCodeRequest, SMSCodeResponse, MobileLoginRequest
from app.api.schemas.user import Token
from app.services.sms_service import sms_service
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.core.redis_client import get_redis_client
from app.api.schemas.role import InsetRole

router = APIRouter()
logger = get_logger(__name__)


@router.post("/send-code", response_model=ResponseModel[SMSCodeResponse], summary="发送短信验证码（注册/登录）")
async def send_sms_code(request: SMSCodeRequest):
    """发送短信验证码"""
    try:
        logger.info(f"发送短信验证码请求: {request.mobile}")

        # 调用短信服务发送验证码
        result = await sms_service.send_verification_code(request.mobile)

        response_data = SMSCodeResponse(
            success=result["success"],
            message=result["message"],
            request_id=result.get("request_id"),
        )

        if result["success"]:
            logger.info(f"短信验证码发送成功: {request.mobile}")
            return send_data(True, response_data, "验证码发送成功")
        else:
            logger.warning(
                f"短信验证码发送失败: {request.mobile}, 原因: {result['message']}"
            )
            return send_data(False, response_data, result["message"])

    except Exception as e:
        logger.error(f"发送短信验证码异常: {request.mobile}, 错误: {str(e)}")
        return send_data(False, None, "验证码发送失败，请稍后重试")


@router.post("/mobile-login", response_model=ResponseModel[Token], summary="手机号验证码登录/注册(如果已存在则直接登录)")
async def mobile_login(request: MobileLoginRequest):
    """手机号验证码登录/注册"""
    try:
        logger.info(f"手机号验证码登录请求: {request.mobile}")

        # 验证短信验证码
        is_valid = await sms_service.verify_code(request.mobile, request.code)
        if not is_valid:
            logger.warning(f"验证码验证失败: {request.mobile}")
            return send_data(False, None, "验证码错误或已过期")

        # 查找用户是否已存在
        user = (
            await User.filter(mobile=request.mobile, is_deleted=False)
            .prefetch_related("role", "organization")
            .first()
        )

        is_new_user = False

        if not user:
            # 用户不存在，创建新用户
            is_new_user = True
            logger.info(f"创建新用户: {request.mobile}")

            # 获取默认组织（如果没有则创建一个默认组织）
            default_org = await Organizations.filter(is_deleted=False).first()
            logger.info(f"默认组织: {default_org}")
            if not default_org:
                # 创建默认组织
                default_org = await Organizations.create(
                    id=uuid.uuid4(),
                    name="默认组织",
                    code="default_org",  # 机构代码（必填）
                    type="系统默认",  # 机构类型（必填）
                    contact_person="系统管理员",  # 联系人（必填）
                    contact_phone="************",  # 联系电话（必填）
                    contact_email="<EMAIL>",  # 电子邮箱（必填）
                    limit_count=10000,  # 机构总可用次数（必填）
                    use_count=0,  # 机构使用次数
                    is_active=True,  # 是否启用
                    is_trial=False,  # 是否体验机构
                    description="系统默认组织，用于手机号注册用户",
                    is_deleted=False,
                )
                logger.info(f"创建默认组织: {default_org.id}")

            # 获取默认角色（普通用户角色）
            default_role = await Role.filter(name="user", is_deleted=False).first()
            logger.info(f"默认角色: {default_role}")
            if not default_role:
                # 创建默认用户角色
                default_role = await Role.create(
                    identifier=InsetRole.ADMIN, 
                    name="管理员", 
                    organization=default_org
                )
                logger.info(f"创建默认用户角色: {default_role.id}")
            # 生成复杂随机密码
            user_password = generate_complex_password()
            # 创建新用户
            user = await User.create(
                id=uuid.uuid4(),
                username=uuid.uuid4(),
                mobile=request.mobile,
                hashed_password=get_password_hash(
                    user_password
                ),  # 手机号登录不需要密码，设置随机密码
                organization_id=default_org.id,
                role_id=default_role.id,
                is_deleted=False,
            )

            # 预加载关联对象
            await user.fetch_related("role", "organization")

            logger.info(f"新用户创建成功: {user.id}")
        else:
            logger.info(f"用户已存在，直接登录: {user.id}")

        # 生成JWT token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=json.dumps({"username": user.username}),
            expires_delta=access_token_expires,
        )


        # 构建响应数据
        response_data = Token(access_token=access_token)

        logger.info(f"手机号登录成功: {user.username}, 新用户: {is_new_user}")
        return send_data(
            True, response_data, "登录成功" if not is_new_user else "注册并登录成功"
        )

    except Exception as e:
        logger.error(f"手机号登录异常: {request.mobile}, 错误: {str(e)}")
        return send_data(False, None, "登录失败，请稍后重试")
