from fastapi import APIRouter, Query
from pydantic import UUID4
from app.api.repository.upload_file import get_file_content_by_id
from app.utils.utils import send_data, ResponseModel
from app.api.schemas.upload_file import UploadFileResponse
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.get("/content", response_model=ResponseModel[UploadFileResponse], summary="获取文件内容（通过文件id）")
async def get_upload_file_content(
    file_id: UUID4 = Query(..., description="文件ID")
):
    """
    通过文件id获取文件内容（基础信息，不直接返回大文本内容）。
    """
    try:
        result = await get_file_content_by_id(file_id)
        return send_data(True, result)
    except Exception as e:
        logger.error(f"获取文件内容失败: {str(e)}")
        return send_data(False, None, f"获取文件内容失败: {str(e)}") 