from fastapi import APIRouter, Query, status, Depends, HTTPException
from datetime import datetime
from uuid import UUID
from app.api.schemas.organizations import (
    OrganizationCreate, 
    OrganizationUpdate, 
    OrganizationResponse, 
    OrganizationWithUserResponse
)
from app.models.organizations import Organizations
from app.utils.utils import (
    send_data,
    ResponseModel,
    send_page_data,
    PageQuery,
    ResponsePageModel
)
from app.models.user import User
from app.core.security import get_password_hash, generate_complex_password
from app.core.config import settings
from app.models.role import Role
from app.api.schemas.role import InsetRole
from app.models.user_report_usage import UserReportUsage
from tortoise.expressions import Q

router = APIRouter()

@router.post("", response_model=ResponseModel[OrganizationWithUserResponse], status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization: OrganizationCreate,
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    创建新机构
    """
    # 检查机构代码是否已存在（排除已删除的）
    existing = await Organizations.filter(code=organization.code).first()
    if existing:
        return send_data(False, None, f"机构代码 '{organization.code}' 已被使用")
    
    try:
        # 创建新机构
        new_org = await Organizations.create(
            **organization.model_dump()
        )
        
        # 创建管理员角色
        admin_role = await Role.create(
            identifier=InsetRole.ADMIN,
            name="管理员",
            organization=new_org
        )
        
        # 生成复杂随机密码
        admin_password = generate_complex_password()
        
        # 创建管理员用户
        admin_user = await User.create(
            username=f"{new_org.code}_admin",
            hashed_password=get_password_hash(admin_password),
            role=admin_role,
            organization=new_org
        )
        
        # 为管理员创建使用次数记录
        await UserReportUsage.create(
            user_id_id=admin_user.id,
            used_count=0,
            max_allowed_count=None
        )
        # 构建响应
        result = {
            **OrganizationResponse.model_validate(new_org, from_attributes=True).model_dump(),
            "username": admin_user.username,
            "password": admin_password
        }
        
        return send_data(True, OrganizationWithUserResponse.model_validate(result))
    except Exception as e:
        return send_data(False, None, f"创建机构失败: {str(e)}")
@router.get("/paginated", response_model=ResponsePageModel[OrganizationResponse], summary="分页获取机构列表")
async def list_page_organizations(
    query: PageQuery = Depends(),
    keyword: str = Query(default=None, description="搜索关键词"),
    contain_deactivated: bool = Query(default=True, description="是否包含已停用机构"),
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    分页获取机构
    """
    try:
        # 查询所有未删除的机构，按修改时间降序排序
        base_query = Organizations.filter(is_deleted=False)
        if keyword:
            queries = Q()
            for field in ["name", "code", "contact_person", "type"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            base_query = base_query.filter(queries)
        if not contain_deactivated:
            base_query = base_query.filter(is_active=True)
        total = await base_query.count()
        orgs = await base_query.order_by("-updated_at").offset(query.page-1).limit(query.size)
        # 返回结果
        org_list = [OrganizationResponse.model_validate(org) for org in orgs]
        return send_page_data(True, {
            "items": org_list,
            "total": total,
            "page": query.page,
            "size": query.size
        })
    except Exception as e:
        return send_page_data(False, None, f"获取机构列表失败: {str(e)}")

@router.get("", response_model=ResponseModel[list[OrganizationResponse]])
async def list_organizations(
    contain_deactivated: bool = Query(default=True, description="是否包含已停用机构"),
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    列出所有未删除的机构
    """
    try:
        # 查询所有未删除的机构，按修改时间降序排序
        orgs = await Organizations.filter(is_deleted=False).order_by("-updated_at") if contain_deactivated else await Organizations.filter(is_deleted=False, is_active=True).order_by("-updated_at")
        
        # 返回结果
        org_list = [OrganizationResponse.model_validate(org) for org in orgs]
        return send_data(True, org_list)
    except Exception as e:
        return send_data(False, None, f"获取机构列表失败: {str(e)}")

@router.get("/{org_id}", response_model=ResponseModel[OrganizationResponse])
async def get_organization(
    org_id: UUID,
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    获取机构详情
    """
    try:
        # 只查询未删除的机构
        org = await Organizations.filter(id=org_id, is_deleted=False).first()
        
        if not org:
            return send_data(False, None, "机构不存在或已被删除")
        
        # 构建详细响应
        response = OrganizationResponse.model_validate(org, from_attributes=True)
        
        # TODO: 添加机构使用统计数据
        # 这里可以从其他表获取使用统计数据，例如用户数、报告数等
        
        return send_data(True, response)
    except Exception as e:
        return send_data(False, None, f"获取机构详情失败: {str(e)}")

@router.put("/{org_id}", response_model=ResponseModel[OrganizationResponse])
async def update_organization(
    org_id: UUID,
    update_data: OrganizationUpdate,
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    更新机构信息
    """
    try:
        # 只能更新未删除的机构
        org = await Organizations.filter(id=org_id, is_deleted=False).first()
        if not org:
            return send_data(False, None, "机构不存在或已被删除")
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        if update_dict:
            # 添加审计字段
            update_dict["updated_at"] = datetime.now()
            # 执行更新
            await org.update_from_dict(update_dict).save()
        
        # 返回更新后的数据
        await org.refresh_from_db()
        return send_data(True, OrganizationResponse.model_validate(org, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新机构失败: {str(e)}")


@router.delete("/{org_id}", response_model=ResponseModel[OrganizationResponse])
async def delete_organization(
    org_id: UUID,
    removed: bool = Query(False, description="是否物理删除"),
    # current_user: UserResponse = Depends(auth_depend)
):
    """
    删除机构 (逻辑删除)
    """
    try:
        org = await Organizations.filter(id=org_id).first()
        if not org:
            return send_data(False, None, "机构不存在")
        
        # 如果已经被逻辑删除，返回相应提示
        if org.is_deleted:
            return send_data(False, None, "该机构已被删除")
        
        if removed:
            # 物理删除
            await org.delete()
            return send_data(True, None)
        else:
            # 执行逻辑删除
            org.is_deleted = True
            org.deleted_at = datetime.now()
            await org.save()
            
            return send_data(True, OrganizationResponse.model_validate(org, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除机构失败: {str(e)}")

