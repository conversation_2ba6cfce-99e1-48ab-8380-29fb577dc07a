from fastapi import APIRouter, Depends, Query
from typing import List, Optional
from uuid import UUID
from datetime import datetime
from app.models.dictionary import Dictionary
from app.api.schemas.dictionary import DictionaryCreate, DictionaryUpdate, DictionaryQuery, DictionaryResponse
from app.utils.utils import send_data, ResponseModel

router = APIRouter()

@router.post("", response_model=ResponseModel[DictionaryResponse])
async def create_dictionary(
    dictionary: DictionaryCreate
):
    """创建字典项"""
    db_dictionary = await Dictionary.create(
        remark=dictionary.remark,
        value=dictionary.value,
        label=dictionary.label,
        category=dictionary.category,
        category_value=dictionary.category_value,
        is_deleted=dictionary.is_deleted,
        deleted_at= datetime.now() if dictionary.is_deleted else None
    )
    return send_data(True, DictionaryResponse.model_validate(db_dictionary, from_attributes=True))

# 创建一个包装器，使 Depends 可选
def get_optional_query(
    contain_deleted: Optional[bool] = Query(False),
    category: Optional[str] = Query(None),
    category_value: Optional[str] = Query(None),
) -> DictionaryQuery:
    return DictionaryQuery(
        contain_deleted=contain_deleted,
        category=category,
        category_value=category_value,
    )
@router.get("", response_model=ResponseModel[List[DictionaryResponse]])
async def list_dictionaries(
    params: DictionaryQuery = Depends(get_optional_query)
    # 类别
):
    contain_deleted, category, category_value = params.contain_deleted, params.category, params.category_value
    """获取字典列表"""
    query = Dictionary.filter() if contain_deleted else Dictionary.filter(is_deleted=False)
    if category:
        query = query.filter(category=category)
    if category_value:
        query = query.filter(category_value=category_value)
    result = await query.all()
    data = [
        DictionaryResponse.model_validate(item, from_attributes=True)
        for item in result
    ]
    return send_data(True, data)

@router.put("/{dictionary_id}", response_model=ResponseModel[DictionaryResponse])
async def update_dictionary(
    dictionary_id: UUID,
    dictionary: DictionaryUpdate
):
    """更新字典项"""
    db_dictionary = await Dictionary.filter(id=dictionary_id, is_deleted=False).first()
    if not db_dictionary:
        return send_data(False, "字典项不存在")
    
    update_data = dictionary.dict(exclude_unset=True)
    await db_dictionary.update_from_dict(update_data)
    await db_dictionary.save()
    return send_data(True, db_dictionary)

@router.delete("/{dictionary_id}")
async def delete_dictionary(
    dictionary_id: UUID
):
    """删除字典项（软删除）"""
    dictionary = await Dictionary.filter(id=dictionary_id, is_deleted=False).first()
    if not dictionary:
        return send_data(False, "字典项不存在")
    dictionary.is_deleted = True
    dictionary.deleted_at = datetime.now()
    await dictionary.save()
    return send_data(True, "字典项删除成功") 