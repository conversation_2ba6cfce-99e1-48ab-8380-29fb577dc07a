from fastapi import APIRouter, Request
from typing import List
from app.api.repository.user_default_model import (
    get_user_default_models,
    create_user_default_models
)
from app.api.schemas.user_default_model import UserDefaultModelCreate, UserDefaultUseResponse
from app.api.deps import get_current_user_from_state
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.api.schemas.user_default_model import (
  UserDefaultModelCreate,
  UserDefaultUseResponse
)
from app.utils.enum import UserDefaultModelError, ModelConfigError
from app.models.model_config import ModelConfig

logger = get_logger(__name__)
router = APIRouter()

@router.get("", response_model=ResponseModel[List[UserDefaultUseResponse]], summary="获取用户的默认模型列表")
async def api_get_user_default_models(
  request: Request
):
    """
    获取用户的默认模型
    """
    try:
      current_user = get_current_user_from_state(request)
      result = await get_user_default_models(current_user)
      return send_data(True, result)
    except Exception as e:
      error_msg = f"{UserDefaultModelError.GET_FAIL.value}: {str(e)}"
      logger.error(error_msg)
      return send_data(False, None, error_msg)

@router.post("", response_model=ResponseModel[bool], summary="修改用户的默认模型")
async def api_create_user_default_models(
  request: Request,
  data: UserDefaultModelCreate
):
    """
    修改用户的默认模型
    """
    try:
      for model in data.list_model:
         model_data = await ModelConfig.filter(
            is_deleted=False,
            id=model.model_id
         )
         if not model_data:
            error_msg = f"{ModelConfigError.NOT_RECORD.value}: {model.model_id}"
            logger.error(error_msg)
            return send_data(False, None, error_msg)
      current_user = get_current_user_from_state(request)
      result = await create_user_default_models(current_user, data)
      return send_data(True, result)
    except Exception as e:
      error_msg = f"{UserDefaultModelError.CREATE_FAIL.value}: {str(e)}"
      logger.error(error_msg)
      return send_data(False, None, error_msg)
