from datetime import <PERSON><PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request, Header
from fastapi.security import OAuth2PasswordRequestForm
from typing import Optional
import json
from app.core.config import settings
from app.core.security import create_access_token, verify_password
from app.models.user import User
from app.api.schemas.user import Token
from app.utils.utils import send_data, ResponseModel
from app.utils.crypto import sm4_decrypt
from app.core.logging import get_logger
from app.models.user_report_usage import UserReportUsage
from app.models.organizations import Organizations
from app.api.schemas.user import UserResponse
from app.api.schemas.role import InsetRole
from app.core.redis_client import get_redis_client
from app.api.schemas.saas_auth import SaasLoginRequest, SaasLoginResponse
from app.services import saas_auth_service

router = APIRouter()
# 获取logger实例
logger = get_logger(__name__)

@router.post("/login_old", response_model=ResponseModel[Token])
async def login(
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """用户登录"""
    try:
        logger.info(f"用户登录: {form_data.username}")
        
        user = await User.filter(username=form_data.username, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user or not verify_password(form_data.password, user.hashed_password):
            return send_data(False, None, "用户名或密码错误")
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=json.dumps({
                "username": user.username
            }), expires_delta=access_token_expires
        )
        if settings.IS_OPEN_SSO == 'ON':
            # 单点登录：将 token 存入 redis，key 为 sso:{username}，value 为 access_token
            redis = get_redis_client()
            redis_key = f"sso:{user.username}"
            # 如果已经有用户登录过，那么删除之前的 token
            if await redis.get(redis_key):
                await redis.delete(redis_key)
            await redis.set(
                redis_key,
                access_token,
                ex=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        token_data = {"access_token": access_token}
        return send_data(True, Token.model_validate(token_data))
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return send_data(False, None, f"登录失败: {str(e)}")


@router.post("/login", response_model=ResponseModel[Token])
async def encrypted_login(
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """加密登录接口
    
    前端使用SM4加密密码，后端解密后验证
    使用与标准登录相同的OAuth2PasswordRequestForm接收参数
    """
    try:
        logger.info(f"加密登录: {form_data.username}")
        
        # 检查用户是否存在
        user = await User.filter(username=form_data.username, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user:
            return send_data(False, None, "用户名或密码错误")

        # 解密密码
        try:
            decrypted_password = sm4_decrypt(form_data.password)
            if not decrypted_password:
                return send_data(False, None, "密码解密失败")
        except Exception as e:
            return send_data(False, None, f"密码解密失败: {str(e)}")
        
        # 验证密码
        if not verify_password(decrypted_password, user.hashed_password):
            return send_data(False, None, "用户名或密码错误")
        
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=json.dumps({
                "username": user.username
            }), expires_delta=access_token_expires
        )
        if settings.IS_OPEN_SSO == 'ON':
            # 单点登录：将 token 存入 redis，key 为 sso:{username}，value 为 access_token
            redis = get_redis_client()
            redis_key = f"sso:{user.username}"
            # 如果已经有用户登录过，那么删除之前的 token
            if await redis.get(redis_key):
                await redis.delete(redis_key)
            await redis.set(
                redis_key,
                access_token,
                ex=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        token_data = {
            "access_token": access_token
        }
        
        logger.info(f"🎉 登录成功 - 用户: {user.username}")
        return send_data(True, Token.model_validate(token_data))
    except Exception as e:
        logger.error(f"💥 登录失败 - 异常: {str(e)}", exc_info=True)
        return send_data(False, None, f"登录失败: {str(e)}")

@router.post("/saas/login", response_model=ResponseModel[SaasLoginResponse], summary="SaaS平台登录")
async def saas_login(request: SaasLoginRequest):
    """
    处理来自第三方SaaS平台的登录请求。
    """
    try:
        jwt_token, redirect_url = await saas_auth_service.login_from_saas(
            platform_identifier=request.platform,
            code=request.code,
            redirect_url=request.redirect_url
        )

        response_data = SaasLoginResponse(
            jwt_token=jwt_token,
            redirect_url=redirect_url
        )

        logger.info(f"🎉 {request.platform} SaaS登录成功")
        return send_data(True, response_data)
    except Exception as e:
        logger.error(f"💥 SaaS登录失败: {str(e)}", exc_info=True)
        return send_data(False, None, f"登录失败: {str(e)}")