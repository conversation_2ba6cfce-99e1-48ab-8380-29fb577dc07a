from fastapi import APIRouter, Depends
from app.api.routes import (
  auth,
  users,
  model_configs,
  user_report_usages,
  dictionary,
  area,
  organizations,
  menu,
  role,
  organization_role_menu,
  organization_menu,
  organization_model,
  user_default_model,
  project_configs,
  project_report,
  project_downloads,
  sms
)
from app.api.deps import (
  get_current_user,
  auth_super_admin_depend,
  auth_not_trial_depend
)



api_router = APIRouter(prefix="/api")
auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user)])
super_admin_auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user), Depends(auth_super_admin_depend)])
# 非体验用户才有的权限路由
no_trial_auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user), Depends(auth_not_trial_depend)])

# 添加无需认证的路由（登录相关接口）
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(sms.router, prefix="/sms", tags=["短信服务"])
auth_router.include_router(role.router, prefix="/role", tags=["角色管理"])
auth_router.include_router(menu.router, prefix="/menu", tags=["菜单管理"])
auth_router.include_router(organization_menu.router, prefix="/organization-menu", tags=["机构菜单管理"])
auth_router.include_router(organization_role_menu.router, prefix="/role-menu", tags=["角色菜单管理"]) 
auth_router.include_router(users.router, prefix="/users", tags=["用户管理"])
auth_router.include_router(user_report_usages.router, tags=["用户报告使用次数"])
super_admin_auth_router.include_router(model_configs.router, prefix="/model-configs", tags=["模型配置管理"]) 
auth_router.include_router(dictionary.router, prefix="/dictionary", tags=["字典"])
auth_router.include_router(area.router, prefix="/area", tags=["省市区"])
super_admin_auth_router.include_router(organizations.router, prefix="/organizations", tags=["机构管理"])
auth_router.include_router(organization_model.router, prefix="/organization-model", tags=["机构的模型信息"]) 
auth_router.include_router(user_default_model.router, prefix="/user-model", tags=["用户的模型"]) 
auth_router.include_router(project_configs.router, prefix="/project-configs", tags=["项目配置"])
auth_router.include_router(project_report.router, prefix="/project-reports", tags=["项目大纲报告生成"])
no_trial_auth_router.include_router(project_downloads.router, prefix="/project-downloads", tags=["项目文件下载"])

api_router.include_router(auth_router)
api_router.include_router(super_admin_auth_router)
api_router.include_router(no_trial_auth_router)