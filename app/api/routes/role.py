from fastapi import APIRouter, Query, status, Depends, Request
from typing import List, Optional
from uuid import UUID
from datetime import datetime
from app.api.deps import get_current_user_from_state
from app.api.schemas.user import UserResponse
from app.models.role import Role
from app.api.schemas.role import RoleCreate, RoleUpdate, RoleResponse, InsetRole, RoleWithOrganizationResponse, PERMISSION_LEVELS
from app.utils.utils import (
  send_data,
  ResponseModel,
  send_page_data,
  ResponsePageModel,
  PageQuery
)
from tortoise.expressions import Q
from app.models.organizations import Organizations
from app.core.logging import get_logger


logger = get_logger(__name__)
router = APIRouter()

@router.post("", response_model=ResponseModel[RoleResponse], status_code=status.HTTP_201_CREATED)
async def create_role(
    role: RoleCreate,
    request: Request
):
    """创建新角色"""
    current_user = get_current_user_from_state(request)
    try:
        # 超级管理员不允许创建
        if role.identifier == InsetRole.SUPER_ADMIN:
            return send_data(False, None, f"角色标识符{InsetRole.SUPER_ADMIN.value}不允许创建")
        org_id = role.organization
        # 如果是超级管理员
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            if not role.organization:
                return send_data(False, None, "超级管理员创建角色时，机构ID不能为空")
        else:
            if not current_user.organization:
                return send_data(False, None, "当前用户的机构不存在")
            org_id = current_user.organization.id
        organization = await Organizations.get_or_none(id=org_id, is_deleted=False)
        if not organization:
            return send_data(False, None, f"机构不存在或被删除")
        # 检查标识符是否已存在
        existing_role = await Role.filter(
            identifier=role.identifier,
            is_deleted=False,
            organization_id=org_id
        ).first()
        if existing_role:
            return send_data(False, None, f"角色标识符{role.identifier}已存在")
        operator_level = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
        deleted_level = PERMISSION_LEVELS.get(role.identifier) or 0
        if operator_level <= deleted_level:
            return send_data(False, None, "权限不足")
        db_role = await Role.create(
            name=role.name,
            identifier=role.identifier,
            organization=organization
        )
        return send_data(True, RoleResponse.model_validate(db_role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"创建角色失败: {str(e)}")
@router.get("/paginated", response_model=ResponsePageModel[RoleWithOrganizationResponse], summary="分页获取角色列表")
async def list_page_roles(
    request: Request,
    keyword: str = Query(default=None, description="搜索关键词"),
    query: PageQuery = Depends(),
    organization_id: Optional[UUID] = Query(None, description="机构ID，超级管理员可选，机构管理员忽略此参数")
):
    """分页获取角色列表"""
    current_user = get_current_user_from_state(request)
    try:
        org_id = organization_id if current_user.role.identifier == InsetRole.SUPER_ADMIN else current_user.organization.id
        if org_id:
            # 🔒 安全检查：验证机构是否存在且未删除
            organization = await Organizations.filter(
                id=org_id, 
                is_deleted=False
            ).first()
            if not organization:
                logger.warning(f"尝试查询不存在的机构角色: {org_id}")
                return send_page_data(False, None, "机构不存在或已被删除")
        
        skip = query.page - 1
        # 查询指定机构的角色
        base_query = Role.filter(is_deleted=False,organization=org_id) if org_id else Role.filter(is_deleted=False)
        if keyword:
            queries = Q()
            for field in ["name", "identifier"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            base_query = base_query.filter(queries)
        total = await base_query.count()
        roles = await base_query.prefetch_related("organization").order_by("-updated_at").offset(skip).limit(query.size)
        return send_page_data(True, {
            "items": [RoleWithOrganizationResponse.model_validate(role, from_attributes=True) for role in roles],
            "total": total,
            "size": query.size,
            "page": query.page
        })
    except Exception as e:
        logger.error(f"用户 {current_user.username} 获取角色列表失败: {str(e)}")
        return send_data(False, None, f"获取角色列表失败: {str(e)}")

@router.get("", response_model=ResponseModel[List[RoleResponse]], summary="不分页获取角色列表")
async def list_roles(
    request: Request,
    keyword: str = Query(default=None, description="搜索关键词"),
    organization_id: Optional[UUID] = Query(None, description="机构ID，超级管理员必填，机构管理员忽略此参数")
):
    """获取角色列表"""
    current_user = get_current_user_from_state(request)
    try:
        org_id = organization_id if current_user.role.identifier == InsetRole.SUPER_ADMIN else current_user.organization.id
        if org_id:
            # 🔒 安全检查：验证机构是否存在且未删除
            organization = await Organizations.filter(
                id=org_id, 
                is_deleted=False
            ).first()
            if not organization:
                logger.warning(f"尝试查询不存在的机构角色: {org_id}")
                return send_data(False, None, "机构不存在或已被删除")
        # 查询指定机构的角色
        base_query = Role.filter(is_deleted=False,organization=org_id) if org_id else Role.filter(is_deleted=False)
        if keyword:
            queries = Q()
            for field in ["name", "identifier"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            base_query = base_query.filter(queries)
        roles = await base_query.order_by("-updated_at").all()
        return send_data(True, [RoleResponse.model_validate(role, from_attributes=True) for role in roles])
    except Exception as e:
        logger.error(f"用户 {current_user.username} 获取角色列表失败: {str(e)}")
        return send_data(False, None, f"获取角色列表失败: {str(e)}")

@router.get("/{role_id}", response_model=ResponseModel[RoleResponse])
async def get_role(
    role_id: UUID,
    request: Request
):
    """获取特定角色详情"""
    try:
        current_user = get_current_user_from_state(request)
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            role = await Role.filter(id=role_id, is_deleted=False).first()
            if not role:
                return send_data(False, None, "角色不存在")
            return send_data(True, RoleResponse.model_validate(role, from_attributes=True))
        else:
            role = await Role.filter(id=role_id, is_deleted=False, organization=current_user.organization.id).first()
            if not role:
                return send_data(False, None, "角色不存在")
            return send_data(True, RoleResponse.model_validate(role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"获取角色详情失败: {str(e)}")

@router.put("/{role_id}", response_model=ResponseModel[RoleResponse])
async def update_role(
    role_id: UUID,
    role: RoleUpdate,
    request: Request
):
    """更新角色信息"""
    current_user = get_current_user_from_state(request)
    try:
        # 超级管理员不允许创建
        if role.identifier == InsetRole.SUPER_ADMIN:
            return send_data(False, None, f"角色标识符{InsetRole.SUPER_ADMIN.value}不允许创建")
        org_id = role.organization
        # 如果是超级管理员
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            if not role.organization:
                return send_data(False, None, "超级管理员编辑角色时，机构ID不能为空")
        else:
            if not current_user.organization:
                return send_data(False, None, "当前用户的机构不存在")
            org_id = current_user.organization.id
        organization = await Organizations.get_or_none(id=org_id, is_deleted=False)
        if not organization:
            return send_data(False, None, f"机构不存在或被删除")
        db_role = await Role.filter(
            id=role_id,
            is_deleted=False,
            organization_id=org_id
        ).first()
        if not db_role:
            return send_data(False, None, "角色不存在")
        operator_level = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
        deleted_level = PERMISSION_LEVELS.get(db_role.identifier) or 0
        if operator_level <= deleted_level:
            return send_data(False, None, "权限不足")
        # 如果更新标识符，检查是否已存在
        if role.identifier and role.identifier != db_role.identifier:
            existing_role = await Role.filter(
                identifier=role.identifier,
                is_deleted=False,
                organization_id=org_id
            ).first()
            if existing_role:
                return send_data(False, None, "角色标识符已存在")
        update_data = role.model_dump(exclude_unset=True)
        if "organization" in update_data:
            update_data.pop("organization")
        update_data["organization_id"] = org_id
        await db_role.update_from_dict(update_data)
        await db_role.save()
        return send_data(True, RoleResponse.model_validate(db_role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新角色失败: {str(e)}")

@router.delete("/{role_id}", response_model=ResponseModel[RoleResponse])
async def delete_role(
    role_id: UUID,
    request: Request
):
    """删除角色（软删除）"""
    current_user = get_current_user_from_state(request)
    try:
        role = None
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            role = await Role.filter(id=role_id, is_deleted=False).first()
        else:
            role = await Role.filter(id=role_id, is_deleted=False, organization=current_user.organization.id).first()
            if not current_user.role:
                return send_data(False, None, "用户没有角色，权限不足")
        if not role:
            return send_data(False, None, "角色不存在")
        operator_level = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
        deleted_level = PERMISSION_LEVELS.get(role.identifier) or 0
        if operator_level <= deleted_level:
            return send_data(False, None, "权限不足")
        role.is_deleted = True
        role.deleted_at = datetime.now()
        await role.save()
        return send_data(True, RoleResponse.model_validate(role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除角色失败: {str(e)}") 