from fastapi import APIRouter, Depends, status, Query, Request
from typing import List, Optional
from datetime import datetime
from uuid import UUID

from app.api.deps import get_current_user_from_state
from app.api.schemas.user import UserResponse
from app.api.schemas.organization_role_menu import AssignMenusToOrganizationRole, RoleMenu
from app.models.organization_role_menu import OrganizationRoleMenu
from app.models.organizations import Organizations
from app.models.role import Role
from app.models.menu import Menu
from app.api.schemas.role import InsetRole
from app.api.schemas.menu import MenuTreeResponse
from app.utils.utils import send_data, ResponseModel
from app.models.organization_menu import OrganizationMenu

router = APIRouter()

# @router.post("/admin", status_code=status.HTTP_201_CREATED, response_model=ResponseModel[bool], summary="给机构管理员分配菜单")
# async def assign_menus_to_organization_admin(
#     request: AssignMenusToOrganizationAdmin,
#     current_user: UserResponse = Depends(get_current_user)
# ):
#     """给机构管理员分配菜单"""
#     # 只有超级管理员可以执行此操作
#     if current_user.role.identifier != InsetRole.SUPER_ADMIN:
#         return send_data(False, None, "权限不足")
    
#     try:
#         # 检查机构是否存在
#         organization = await Organizations.filter(id=request.organization_id, is_deleted=False).first()
#         if not organization:
#             return send_data(False, None, "机构不存在")
        
#         # 查找机构的管理员角色
#         admin_role = await Role.filter(
#             organization_id=request.organization_id,
#             identifier=InsetRole.ADMIN,
#             is_deleted=False
#         ).first()
        
#         if not admin_role:
#             return send_data(False, None, "机构管理员角色不存在")
        
#         # 检查所有菜单是否存在
#         menus = await Menu.filter(id__in=request.menu_ids, is_deleted=False).all()
#         if len(menus) != len(request.menu_ids):
#             return send_data(False, None, "部分菜单不存在")
        
#         # 1. 先将该角色原有的菜单关联标记为已删除
#         await OrganizationRoleMenu.filter(
#             organization_id=request.organization_id,
#             role_id=admin_role.id,
#             is_deleted=False
#         ).update(is_deleted=True, deleted_at=datetime.now())
        
#         # 2. 创建新的菜单关联
#         new_relations = []
#         for menu_id in request.menu_ids:
#             relation = await OrganizationRoleMenu.create(
#                 organization_id=request.organization_id,
#                 role_id=admin_role.id,
#                 menu_id=menu_id
#             )
#             new_relations.append(relation)
        
#         return send_data(True, True)
#     except Exception as e:
#         return send_data(False, None, f"分配菜单失败: {str(e)}")

@router.post("", status_code=status.HTTP_201_CREATED, response_model=ResponseModel[int], summary="给机构的角色赋予菜单【超级管理员、机构管理员】")
async def assign_menus_to_organization_role(
    request: AssignMenusToOrganizationRole,
    req: Request
):
    """给机构里面除管理员以外的角色分配权限"""
    current_user = get_current_user_from_state(req)
    # 只有超级管理员或机构管理员可以执行此操作
    is_org_admin = current_user.role.identifier == InsetRole.ADMIN and current_user.organization
    if current_user.role.identifier != InsetRole.SUPER_ADMIN and not is_org_admin:
        return send_data(False, None, "权限不足")
    
    try:
        if current_user.role.identifier == InsetRole.SUPER_ADMIN and not request.organization_id:
            return send_data(False, None, "机构ID不能为空")
        organization_id = request.organization_id if current_user.role.identifier == InsetRole.SUPER_ADMIN else current_user.organization.id
        # 检查机构是否存在
        organization = await Organizations.filter(id=organization_id, is_deleted=False).first()
        if not organization:
            return send_data(False, None, "机构不存在")
        
        # 检查角色是否存在
        role = await Role.filter(
            id=request.role_id,
            organization_id=organization_id, 
            is_deleted=False
        ).first()
        
        if not role:
            return send_data(False, None, "角色不存在或不属于该机构")
        
        # 检查所有菜单是否存在
        menus = await Menu.filter(id__in=request.menu_ids, is_deleted=False).all()
        if len(menus) != len(request.menu_ids):
            return send_data(False, None, "部分菜单不存在")
        
        # 1. 先将该角色原有的菜单关联标记为已删除
        await OrganizationRoleMenu.filter(
            organization_id=organization_id,
            role_id=request.role_id,
            is_deleted=False
        ).update(is_deleted=True, deleted_at=datetime.now())
        
        # 2. 创建新的菜单关联
        new_relations = []
        for menu_id in request.menu_ids:
            is_existing = await OrganizationMenu.filter(
                organization_id=organization_id,
                menu_id=menu_id,
                is_deleted=False
            )
            if is_existing:
                relation = await OrganizationRoleMenu.create(
                    organization_id=organization_id,
                    role_id=request.role_id,
                    menu_id=menu_id
                )
                new_relations.append(relation)
        
        return send_data(True, len(new_relations))
    except Exception as e:
        return send_data(False, None, f"分配菜单失败: {str(e)}")
    
@router.get("", response_model=ResponseModel[List[RoleMenu]], summary="获取机构某个角色可用菜单（数组）")
async def list_menus(
    request: Request,
    role_id: UUID = Query(..., description="用户的角色ID"),
    organization_id: Optional[UUID] = Query(None, description="机构的ID")
):
    """获取机构某个角色可用菜单（数组）"""
    current_user = get_current_user_from_state(request)
    role = None
    if current_user.role.identifier == InsetRole.SUPER_ADMIN and not organization_id:
        return send_data(False, None, "机构ID不能为空")
    organization = organization_id if current_user.role.identifier == InsetRole.SUPER_ADMIN else current_user.organization.id
    role = await Role.filter(
        id=role_id,
        organization_id=organization
    ).first()
    if not role:
        return send_data(False, None, "角色不存在")
    try:
        # 执行查询
        organization_menus = await OrganizationRoleMenu.filter(
            organization_id=organization,
            role_id=role.id,
            is_deleted=False
        ).order_by("-created_at").all()
        
        # 转换结果
        result = [
            RoleMenu.model_validate(item, from_attributes=True)
            for item in organization_menus
        ]
        
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"获取机构角色菜单失败: {str(e)}")

@router.get("/tree", response_model=ResponseModel[List[MenuTreeResponse]], summary="获取当前用户角色的菜单列表（树状结构）(超级管理员不可以调用)")
async def get_role_menus(
    request: Request,
    is_flat: bool = Query(default=False, description="是否平铺结果")
):
    """获取当前用户角色的菜单列表（树状结构）"""
    current_user = get_current_user_from_state(request)
    # 只有超级管理员不可以执行此操作
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    
    try:
        # 如果用户有机构先检测机构是否存在
        if current_user.organization:
            organization = await Organizations.filter(id=current_user.organization.id, is_deleted=False).first()
            if not organization:
                return send_data(False, None, "机构不存在")
        
        # 检查角色是否存在
        role = await Role.filter(
            id=current_user.role.id,
            organization_id=current_user.organization.id if current_user.organization else None, 
            is_deleted=False
        ).first()
        
        if not role:
            return send_data(False, None, "角色不存在或不属于该机构")
        
        # 获取角色的菜单ID列表
        role_menu_ids = await OrganizationRoleMenu.filter(
            organization_id=current_user.organization.id if current_user.organization else None,
            role_id=current_user.role.id,
            is_deleted=False
        ).values_list("menu_id", flat=True)
        
        if not role_menu_ids:
            return send_data(True, [])
            
        # 获取这些菜单的完整信息
        menus = await Menu.filter(
            id__in=role_menu_ids,
            is_deleted=False
        ).order_by("-order").all()
        if is_flat:
            return send_data(True, [MenuTreeResponse.model_validate(menu, from_attributes=True) for menu in menus])    
        
        # 构建菜单树
        menu_dict = {}
        root_menus = []
        
        # 先将所有菜单放入字典
        for menu in menus:
            menu_dict[menu.id] = MenuTreeResponse.model_validate(menu, from_attributes=True)
            menu_dict[menu.id].children = []
        
        # 构建树形结构
        for menu in menus:
            menu_response = menu_dict[menu.id]
            if menu.parent_id is None or menu.parent_id not in menu_dict:
                # 没有父菜单或父菜单不在权限内，则作为根菜单
                root_menus.append(menu_response)
            else:
                # 将当前菜单添加到父菜单的children中
                menu_dict[menu.parent_id].children.append(menu_response)
        
        # 排序根菜单（按order字段）
        # root_menus.sort(key=lambda x: x.order)
        
        return send_data(True, root_menus)
    except Exception as e:
        return send_data(False, None, f"获取角色菜单失败: {str(e)}") 