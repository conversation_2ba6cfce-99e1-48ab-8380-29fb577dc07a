from pydantic import BaseModel, UUID4, Field, HttpUrl
from typing import Optional, List
from datetime import datetime

class ProjectUrlSummaryResponse(BaseModel):
  summary: Optional[str] = Field(None, description="url的内容总结")
  project_id: Optional[UUID4] = Field(..., description="项目ID")
  is_valid: Optional[bool] = Field(..., description="是否有效")
  id: UUID4
  url: str
  created_at: Optional[datetime] = Field(..., description="创建时间")
  updated_at: Optional[datetime] = Field(..., description="更新时间")

class ProjectUrlSummaryCreate(BaseModel):
  url: List[HttpUrl] = Field(..., description="网页地址列表")
  name: str = Field(..., description="研究主题")
class ProjectUrlSummaryBind(BaseModel):
  url_ids: List[UUID4] = Field(..., description="网页ID")
  project_id: UUID4 = Field(..., description="项目的ID")
class UrlByIds(BaseModel):
  ids: List[UUID4] = Field(..., description="网页ID")
