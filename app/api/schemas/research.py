from pydantic import BaseModel, UUID4
from typing import Optional, List
from app.models.research import ResearchStatus
from datetime import datetime

class Reference(BaseModel):
    is_valid_reference: bool
    doi: str | None
    url: str | None
    text: str | None
    
class ResearchResponse(BaseModel):
    id: UUID4
    contexts: List[str]
    literatures: List[Reference]
    status: ResearchStatus
    model_config = {
        "from_attributes": True
    }