from pydantic import BaseModel, UUID4, Field
from typing import Optional, List
from app.api.schemas.model_config import ModelConfigBase
from app.models.organization_model_use import UseCase

class UserDefaultUseResponse(BaseModel):
    model: ModelConfigBase = Field(description="模型信息")
    default_way: str = Field(description="使用场景")

class UseItem(BaseModel):
   model_id: UUID4
   default_way: UseCase
class UserDefaultModelCreate(BaseModel):
    list_model: List[UseItem] = Field(description="模型列表")