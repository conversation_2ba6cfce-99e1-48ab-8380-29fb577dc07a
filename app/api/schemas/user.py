from pydantic import BaseModel, UUID4, <PERSON>
from typing import Optional
from datetime import datetime
from enum import Enum
from app.api.schemas.role import RoleResponse
from app.api.schemas.organizations import OrganizationResponse

class UserBase(BaseModel):
    username: str


class UserCreate(UserBase):
    password: str
    realname: Optional[str] = None
    role_id: UUID4
    # role_name: Optional[str] = None
    organization_id: Optional[UUID4] = None
    max_allowed_count: Optional[int] = None


class UserUpdate(BaseModel):
    username: Optional[str] = None
    password: Optional[str] = None
    realname: Optional[str] = None
    role_id: UUID4
    # role_name: Optional[str] = None
    organization_id: Optional[UUID4] = None
    max_allowed_count: Optional[int] = None


class EncryptedLoginRequest(BaseModel):
    """加密登录请求模型"""
    username: str
    password: str


class UserInDB(UserBase):
    id: UUID4
    created_at: datetime
    realname: Optional[str] = None
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None
    role_name: Optional[str] = None
    organization_id: Optional[UUID4] = None
    
    class Config:
        # orm_mode = True
        from_attributes = True


class UserResponse(UserBase):
    id: UUID4
    created_at: datetime
    realname: Optional[str] = None
    updated_at: Optional[datetime] = None
    role: Optional[RoleResponse]
    organization: Optional[OrganizationResponse]
    is_trial: Optional[bool] = False
    is_could_create_report: Optional[bool] = True
    deleted_at: Optional[datetime] = None
    used_count: Optional[int] = None
    max_allowed_count: Optional[int] = None
    
    # role_name: Optional[str] = None
    # organization_id: Optional[UUID4] = None
    # organization_name: Optional[str] = None
    class Config:
        # orm_mode = True
        from_attributes = True


class Token(BaseModel):
    access_token: str

    class Config:
        # orm_mode = True
        from_attributes = True
    # token_type: str = "bearer"
    # is_admin: bool = False
    # role_name: Optional[str] = None
    # is_create_reports: bool = True


class TokenPayload(BaseModel):
    sub: Optional[str] = None 