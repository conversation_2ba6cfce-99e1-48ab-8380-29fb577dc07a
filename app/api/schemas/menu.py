from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List
from uuid import UUID

class MenuBase(BaseModel):
    name: str = Field(..., description="菜单名称")
    identifier: str = Field(..., description="菜单标识符")
    parent_id: Optional[UUID] = Field(None, description="父菜单ID")
    # module_id: UUID = Field(..., description="所属模块ID")

class MenuCreate(MenuBase):
    order: Optional[int]= Field(None, description="菜单顺序")

class MenuUpdate(BaseModel):
    name: Optional[str] = Field(None, description="菜单名称")
    identifier: Optional[str] = Field(None, description="菜单标识符")
    order: Optional[int] = Field(None, description="菜单顺序")
    parent_id: Optional[UUID] = Field(None, description="父菜单ID")
    # module_id: Optional[UUID] = Field(None, description="所属模块ID")

class MenuResponse(MenuBase):
    id: UUID
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    order: int 

    class Config:
        from_attributes = True
class MenuResponse1(MenuBase):
    id: UUID
    name: Optional[str] = Field(None, description="菜单名称")
    identifier: Optional[str] = Field(None, description="菜单标识符")
    order: int
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class MenuTreeResponse(MenuResponse1):
    children: Optional[List['MenuTreeResponse']] = None