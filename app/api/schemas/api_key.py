from pydantic import BaseModel, UUID4
from typing import Optional
from datetime import datetime, timezone


class APIKeyBase(BaseModel):
    name: str
    quota: Optional[int] = 0  # 0表示无限制


class APIKeyCreate(APIKeyBase):
    pass


class APIKeyResponse(APIKeyBase):
    id: UUID4
    key: str
    used: int
    is_active: bool
    created_at: datetime
    
    class Config:
        # orm_mode = True
        from_attributes = True


class APIKeyUpdate(BaseModel):
    name: Optional[str] = None
    quota: Optional[int] = None
    is_active: Optional[bool] = None 