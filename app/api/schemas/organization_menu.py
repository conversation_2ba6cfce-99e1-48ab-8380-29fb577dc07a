from pydantic import BaseModel, Field
from typing import List, Optional
from uuid import UUID
from datetime import datetime

class OrganizationMenuBase(BaseModel):
    organization_id: UUID = Field(..., description="机构ID")
    menu_id: UUID = Field(..., description="菜单ID")

class OrganizationMenuCreate(OrganizationMenuBase):
    pass

class OrganizationMenuBatchCreate(BaseModel):
    organization_id: UUID = Field(..., description="机构ID")
    menu_ids: List[UUID] = Field(..., description="菜单ID列表")

class OrganizationMenuUpdate(BaseModel):
    is_deleted: Optional[bool] = Field(None, description="是否删除")

class OrganizationMenuResponse(OrganizationMenuBase):
    organization_id: UUID = Field(..., description="机构ID")
    menu_id: UUID = Field(..., description="菜单ID")

    class Config:
        from_attributes = True 