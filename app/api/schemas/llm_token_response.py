from pydantic import BaseModel
from typing import Optional
from datetime import datetime, timezone


class OpenRouterGenerationData(BaseModel):
    """OpenRouter生成任务结果数据模型"""
    id: str  # 生成任务的唯一标识符
    upstream_id: Optional[str] = None  # 上游服务的任务ID
    total_cost: float  # 生成任务的总成本
    cache_discount: Optional[float] = None  # 缓存优惠金额
    provider_name: str  # 服务提供商名称
    created_at: datetime  # 生成任务创建时间
    model: str  # 使用的模型名称
    app_id: Optional[str] = None  # 应用ID
    streamed: bool  # 是否使用了流式传输
    cancelled: bool  # 任务是否被取消
    latency: int  # 延迟时间(毫秒)
    moderation_latency: Optional[int] = None  # 内容审核延迟(毫秒)
    generation_time: int  # 生成耗时(毫秒)
    tokens_prompt: int  # 提示词的token数量
    tokens_completion: int  # 完成内容的token数量
    native_tokens_prompt: int  # 原生提示词的token数量
    native_tokens_completion: int  # 原生完成内容的token数量
    native_tokens_reasoning: int  # 推理过程使用的token数量
    num_media_prompt: Optional[int] = None  # 提示中的媒体数量
    num_media_completion: Optional[int] = None  # 完成内容中的媒体数量
    num_search_results: Optional[int] = None  # 搜索结果数量
    origin: str  # 请求来源
    is_byok: bool  # 是否使用客户自有密钥(Bring Your Own Key)
    finish_reason: str  # 完成原因
    native_finish_reason: str  # 原生完成原因
    usage: float  # 资源使用量


class OpenRouterResponse(BaseModel):
    """OpenRouter API响应模型"""
    data: OpenRouterGenerationData  # 生成数据


class LLMGenerationResponse(BaseModel):
    """API服务统一的LLM生成响应模型"""
    success: bool  # 请求是否成功
    data: Optional[OpenRouterGenerationData] = None  # 成功时的生成数据
    error: Optional[str] = None  # 失败时的错误信息
    error_code: Optional[int] = None  # 失败时的错误代码 