from pydantic import BaseModel, Field
from uuid import UUID
from typing import Optional
from datetime import datetime
from fastapi import UploadFile, File

class UploadFileRequest(BaseModel):
    """
    上传文件请求体（实际接口一般直接用 UploadFile，不需要额外字段，这里预留扩展）
    """
    file: UploadFile = File(..., description="用户上传的文件")
    # 预留字段，如需额外参数可在此添加
    pass

class UploadFileResponse(BaseModel):
    """
    上传文件响应体
    """
    id: UUID = Field(..., description="文件ID")
    file_path: str = Field(..., description="文件物理路径")
    file_name: str = Field(..., description="文件名")
    created_at: datetime = Field(..., description="上传时间")
