from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class AreaBase(BaseModel):
    parent_id: Optional[int] = Field(None, description="父级ID")
    name: str = Field(..., description="名称")

class AreaCreate(AreaBase):
    pass

class AreaUpdate(AreaBase):
    name: Optional[str] = Field(None, description="名称")

class AreaResponse(AreaBase):
    id: int
    parent_id: Optional[int] = Field(None, description="父级ID")
    name: str = Field(..., description="名称")
    is_deleted: bool
    deleted_at: Optional[datetime]
    updated_at: Optional[datetime]
    created_at: Optional[datetime]

    class Config:
        from_attributes = True 