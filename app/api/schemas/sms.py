from pydantic import BaseModel, Field, validator
import re


class SMSCodeRequest(BaseModel):
    """发送短信验证码请求"""
    mobile: str = Field(..., description="手机号码")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not v:
            raise ValueError("手机号码不能为空")
        
        # 验证手机号格式（中国大陆11位手机号）
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError("请输入正确的手机号码")
        
        return v


class SMSCodeResponse(BaseModel):
    """发送短信验证码响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    request_id: str = Field(None, description="请求ID")


class MobileLoginRequest(BaseModel):
    """手机号验证码登录请求"""
    mobile: str = Field(..., description="手机号码")
    code: str = Field(..., description="验证码")
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not v:
            raise ValueError("手机号码不能为空")
        
        # 验证手机号格式（中国大陆11位手机号）
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError("请输入正确的手机号码")
        
        return v
    
    @validator('code')
    def validate_code(cls, v):
        if not v:
            raise ValueError("验证码不能为空")
        
        # 验证码应该是6位数字
        if not re.match(r'^\d{6}$', v):
            raise ValueError("验证码格式错误")
        
        return v


class MobileLoginResponse(BaseModel):
    """手机号验证码登录响应"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    user_id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    mobile: str = Field(..., description="手机号")
    organization_id: str = Field(..., description="组织ID")
    is_new_user: bool = Field(..., description="是否为新注册用户")