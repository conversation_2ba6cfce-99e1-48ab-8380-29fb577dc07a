from pydantic import BaseModel, Field
from typing import List, Optional
from uuid import UUID

class AssignMenusToOrganizationAdmin(BaseModel):
    """分配菜单给机构管理员的请求模型"""
    organization_id: UUID = Field(..., description="机构ID")
    menu_ids: List[UUID] = Field(..., description="要分配的菜单ID列表")

class AssignMenusToOrganizationRole(BaseModel):
    """分配菜单给机构特定角色的请求模型"""
    organization_id: Optional[UUID] = Field(None, description="机构ID")
    role_id: UUID = Field(..., description="角色ID")
    menu_ids: List[UUID] = Field(..., description="要分配的菜单ID列表") 
class RoleMenu(BaseModel):
    menu_id: UUID