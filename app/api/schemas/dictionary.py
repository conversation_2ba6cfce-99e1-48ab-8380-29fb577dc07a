from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from uuid import UUID

class DictionaryQuery(BaseModel):
    category: Optional[str] = Field(..., description="分类标签、传材料口径、机构性质、语言风格")
    category_value: Optional[str] = Field(..., description="分类标签value值，这个暂时不用"),
    contain_deleted: Optional[bool] = Field(..., description="是否包含删除的字段，悬浮显示“暂不开放”就是要把这个值设置为True")
class DictionaryBase(BaseModel):
    value: str = Field(..., description="字典值")
    label: str = Field(..., description="展示标签")
    category: str = Field(..., description="分类标签")
    category_value: str = Field(..., description="分类值")
    remark: Optional[str] = Field(None, description="备注信息")

class DictionaryCreate(DictionaryBase):
    is_deleted: Optional[bool] = False

class DictionaryUpdate(DictionaryBase):
    value: Optional[str] = Field(None, description="字典值")
    label: Optional[str] = Field(None, description="展示标签")
    category: Optional[str] = Field(None, description="分类标签")
    category_value: Optional[str] = Field(None, description="分类值")

class DictionaryResponse(DictionaryBase):
    id: UUID
    is_deleted: bool
    deleted_at: Optional[datetime]
    updated_at: Optional[datetime]
    created_at: Optional[datetime]

    class Config:
        from_attributes = True 