from pydantic import BaseModel, Field
from typing import Optional
from uuid import UUID


class LiteratureBase(BaseModel):
    """文献基础模型"""
    title: str = Field(..., description="文献标题")
    authors: str = Field(..., description="作者，多个作者用逗号分隔")
    journal: str = Field(..., description="期刊名称")
    year: int = Field(..., description="发表年份")
    issue: Optional[str] = Field(None, description="期号")
    volume: Optional[str] = Field(None, description="卷号")
    pages: Optional[str] = Field(None, description="页码范围，如：123-145")
    doi: Optional[str] = Field(None, description="DOI索引")
    summary: str = Field(..., description="文献总结")
    url: str = Field(..., description="文献网页链接")


class LiteratureCreate(LiteratureBase):
    """创建文献模型"""
    research_id: UUID = Field(..., description="关联的研究项目ID")

class LiteratureResponse(BaseModel):
    """文献响应模型"""
    # id: UUID
    # research_id: UUID
    doi: Optional[str] = Field(None, description="DOI索引")
    url: Optional[str] = Field(None, description="文献网页链接")
    title: str = Field(..., description="文献标题")
    citation_format: Optional[str] = Field(None, description="标准引用格式")
    summary: str = Field(..., description="文献总结")

    model_config = {"from_attributes": True}