from tortoise import Tortoise
from app.core.config import settings
import os

DATABASE_URL = os.environ.get("DATABASE_URL")

print(f"config.py: DATABASE_URL: {DATABASE_URL}")
if not DATABASE_URL:
    from dotenv import load_dotenv
    from pathlib import Path

    # 找到当前文件夹的 .env 文件（可以根据你的目录结构调整）
    env_path = Path(__file__).parent.parent / ".env"
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        DATABASE_URL = os.environ.get("DATABASE_URL")
    else:
        raise FileNotFoundError(f".env 文件不存在，且没有检测到 DATABASE_URL 环境变量，请检查！")

# 最终确认 DATABASE_URL 必须有
if not DATABASE_URL:
    raise ValueError("DATABASE_URL 环境变量未设置，无法启动 ORM！")

TORTOISE_ORM = {
    "connections": {"default": DATABASE_URL},
    "apps": {
        "models": {
            "models": ["aerich.models", "app.models"],
            "default_connection": "default",
        },
    },
}


async def init_db():
    """初始化数据库连接"""
    await Tortoise.init(
        db_url=settings.DATABASE_URL,
        modules={"models": ["app.models"]},
    )
    # 生成表结构（仅在开发环境使用）
    await Tortoise.generate_schemas()


async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections() 