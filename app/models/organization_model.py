from tortoise import fields
from tortoise.models import Model

class OrganizationModels(Model):
    id = fields.UUIDField(pk=True)
    organization = fields.ForeignKeyField('models.Organizations', related_name='model_related_organization', description="机构")
    model = fields.ForeignKeyField('models.ModelConfig', related_name='organization_related_model', description="模型")
    # default_way = fields.CharEnumField(
    #     UserWay,
    #     description="模型的默认用途",
    #     null=False
    # )
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "organization_models"
        table_description = "机构模型关联表"