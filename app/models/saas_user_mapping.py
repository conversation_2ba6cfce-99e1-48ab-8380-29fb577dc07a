import uuid
from tortoise import fields
from tortoise.models import Model

class SaasUserMapping(Model):
    """第三方用户与本系统用户的映射关系表"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    # 关联到本系统的用户
    user_id = fields.UUIDField(description="关联的用户ID")
    # 第三方平台返回的用户唯一ID
    external_id = fields.CharField(max_length=255, description='第三方平台的用户ID')
    # 平台标识，如 'jiayi'
    platform = fields.CharField(max_length=50, description='来源平台标识')
    mobile = fields.CharField(max_length=50, description='医生手机号，作为用户名')
    name = fields.CharField(max_length=50, description='用户姓名')
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "saas_user_mappings"
        unique_together = ("platform", "external_id") # 确保同一平台的用户ID是唯一的
        description = "SaaS用户映射"

    def __str__(self):
        return f"{self.platform}:{self.external_id} -> User {self.user_id}"