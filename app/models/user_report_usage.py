import uuid
from tortoise import fields
from tortoise.models import Model


class UserReportUsage(Model):
    """用户报告生成使用次数模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    user_id = fields.ForeignKeyField(
        'models.User', related_name='report_usages', description="关联用户ID"
    )
    used_count = fields.IntField(default=0, description="已使用生成报告次数")
    max_allowed_count = fields.IntField(null=True, description="最大允许生成报告次数")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    # 删除时间
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "user_report_usages"
        description = "用户报告生成使用次数"
    
    def __str__(self):
        return f"用户 {self.user_id} 报告使用情况" 