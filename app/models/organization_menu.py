from tortoise import fields
from tortoise.models import Model
from datetime import datetime

class OrganizationMenu(Model):
    id = fields.UUIDField(pk=True)
    organization = fields.ForeignKeyField('models.Organizations', related_name='menus', description="机构")
    menu = fields.ForeignKeyField('models.Menu', related_name='organizations', description="菜单")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "organization_menus"
        table_description = "机构菜单关联表"