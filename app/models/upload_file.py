import uuid
from tortoise import fields
from tortoise.models import Model


class UploadFile(Model):
    """上传的文件"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    file_path = fields.CharField(max_length=255, description="文件路径")
    file_name = fields.Char<PERSON>ield(max_length=255, description="文件名称")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "upload_files"
        description = "用户上传的文件"
    
    def __str__(self):
        return f"{self.file_name}" 