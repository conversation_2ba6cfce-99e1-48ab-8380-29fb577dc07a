import uuid
from tortoise import fields
from tortoise.models import Model
from app.api.schemas.project_configs import ProjectConfigStatus

class ProjectConfig(Model):
    """项目配置模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(null=True, max_length=300, description="项目名称")
    application_category = fields.CharField(max_length=50, description="申报口径", null=True)
    
   
    user = fields.ForeignKeyField(
        "models.User",
        related_name="project_configs",
        description="创建用户"
    )
    model = fields.ForeignKeyField(
        "models.ModelConfig", 
        related_name="model_configs", 
        description="关联的模型配置",
        null=True
    )
    research = fields.ForeignKeyField(
        'models.Research',
        related_name='research',
        description="关联研究表",
        null=True
    )
    
    ai_leader_introduction = fields.CharField(max_length=2000, null=True, description="项目主体的AI介绍")
    word_count_requirement = fields.IntField(null=True, description="字数要求")
    literature_library = fields.TextField(null=True, description="文献库")
    requirements_attachments = fields.JSONField(null=True, description="申报要求附件", default=[])
    language_style = fields.CharField(null=True, max_length=100, description="语言风格")
    estimated_time = fields.DatetimeField(null=True, description="预估时间", use_tz=True)
    
    # AI生成内容相关字段
    ai_generated_outline = fields.TextField(null=True, description="AI生成的大纲")
    outline_generated_time = fields.DatetimeField(null=True, description="大纲生成时间", use_tz=True)
    manual_modified_outline = fields.TextField(null=True, description="人工修改的大纲")
    manual_modified_outline_time = fields.DatetimeField(null=True, description="人工修改大纲的时间", use_tz=True)
    ai_generated_report = fields.TextField(null=True, description="AI生成的报告")
    report_generation_time = fields.DatetimeField(null=True, description="报告的生成时间", use_tz=True)
    manual_modified_report = fields.TextField(null=True, description="人工修改的报告")
    manual_modified_report_time = fields.DatetimeField(null=True, description="人工修改报告的时间", use_tz=True)
    outline_tokens_consumed = fields.IntField(null=True, default=0, description="大纲生成消耗的tokens数量")
    report_tokens_consumed = fields.IntField(null=True, default=0, description="报告生成消耗的tokens数量")
    ai_trace_report = fields.TextField(null=True, description="AI痕迹的报告内容")
    ai_trace_generated_time = fields.DatetimeField(null=True, description="AI痕迹报告的生成时间", use_tz=True)
    hallucination_report = fields.TextField(null=True, description="幻觉审查的报告内容")
    hallucination_generated_time = fields.DatetimeField(null=True, description="幻觉审查的生成时间", use_tz=True)
    # 用户增加的提示词
    user_add_prompt = fields.TextField(null=True, description="用户增加的提示词")
    # 用户增加的大纲模板
    user_add_demo = fields.ForeignKeyField(
        'models.UploadFile',
        related_name='upload_file',
        description="关联文件上传表",
        null=True
    )
    # 项目状态
    status = fields.CharField(
        max_length=20, 
        default=ProjectConfigStatus.CONFIGURING.value, 
        description="""项目状态 (
        CONFIGURING,
        OUTLINE_GENERATING,
        OUTLINE_GENERATED,
        OUTLINE_FAILED,
        REPORT_GENERATING,
        REPORT_GENERATED,
        REPORT_FAILED,
        REMOVE_HALLUCINATING,
        REMOVE_HALLUCINATED,
        REMOVE_AI_TRACING,
        REMOVE_AI_TRACED
        )"""
    )
    
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    # 软删除字段
    is_deleted = fields.IntField(default=0, description="是否删除 (0: 正常, 1: 删除)")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "project_configs"
        description = "项目配置"
    
    def __str__(self):
        return f"{self.name}" 