from tortoise import fields
from tortoise.models import Model

class ProjectUrlSummary(Model):
    id = fields.UUIDField(pk=True, description="主键")
    url = fields.CharField(max_length=512, description="网页地址")
    summary = fields.TextField(null=True, description="url的内容总结")
    is_valid = fields.BooleanField(null=True, description="是否有效，如果为None说明还没核验")
    project = fields.ForeignKeyField('models.ProjectConfig', null=True, related_name='urls_project', description="项目ID")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    user = fields.ForeignKeyField(
        "models.User",
        related_name="urls_user",
        description="创建用户"
    )
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "docgen_project_url_summary"
        table_description = "项目用户自主输入的网页链接及内容总结表"
