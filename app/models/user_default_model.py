from tortoise import fields
from tortoise.models import Model
from app.models.organization_model_use import UseCase

class UserDefaultModels(Model):
    id = fields.UUIDField(pk=True)
    user = fields.ForeignKeyField('models.User', related_name='model_related_user', description="用户")
    model = fields.ForeignKeyField('models.ModelConfig', related_name='user_related_model', description="模型")
    default_way = fields.CharEnumField(UseCase, description="模型的默认用途")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "user_default_models"
        # 不是用户可以使用的所有模型哦，这里只是保存了用户三个用途的默认模型
        table_description = "用户的默认模型关联表"