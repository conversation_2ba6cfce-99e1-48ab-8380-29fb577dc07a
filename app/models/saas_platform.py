import uuid
from tortoise import fields
from tortoise.models import Model

class SaasPlatform(Model):
    """SaaS平台配置表"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    platform = fields.CharField(max_length=50, unique=True, description='平台唯一标识，如 "jiayi"')
    platform_name = fields.CharField(max_length=100, description='平台展示名称')
    token_url = fields.CharField(max_length=512, description='获取token的完整URL')
    userinfo_url = fields.CharField(max_length=512, description='获取医生信息的URL')
    client_id = fields.Char<PERSON>ield(max_length=100, description='客户端ID')
    client_secret = fields.Char<PERSON>ield(max_length=100, description='客户端密钥')
    is_active = fields.BooleanField(default=True, description="是否启用")
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "saas_platforms"
        description = "SaaS平台配置"

    def __str__(self):
        return self.platform_name