from tortoise import fields
from tortoise.models import Model


class Organizations(Model):
    id = fields.UUIDField(pk=True)
    name = fields.CharField(max_length=100, description="机构名称")
    code = fields.CharField(max_length=50, description="机构代码(唯一)")
    type = fields.CharField(max_length=50, description="机构类型")
    is_active = fields.BooleanField(default=True, description="是否启用")
    
    # 是否是体验机构
    is_trial = fields.BooleanField(default=False, null=True, description="是否是体验机构")
    # 联系信息
    contact_person = fields.CharField(max_length=50, description="联系人")
    contact_phone = fields.CharField(max_length=20, description="联系电话")
    contact_email = fields.CharField(max_length=100, description="电子邮箱")
    # 机构总的可用次数
    limit_count = fields.IntField(null=True, description="机构的总可用次数")
    # 机构的使用次数
    use_count = fields.IntField(null=True, description="机构的使用次数")
    # 审计字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(null=True, description="最后修改时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    # 额外信息
    description = fields.TextField(null=True, description="机构描述")
    remarks = fields.TextField(null=True, description="备注")
    
    class Meta:
        table = "organizations"
        
    def __str__(self):
        return f"{self.name} ({self.code})" 