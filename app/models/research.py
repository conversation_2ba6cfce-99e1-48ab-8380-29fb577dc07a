import uuid
from enum import Enum
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class ResearchStatus(str, Enum):
    """研究状态枚举"""
    PENDING = "pending"        # 待处理
    SEARCHING = "searching"    # 搜索中
    ANALYZING = "analyzing"    # 分析中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"          # 失败


class Research(Model):
    """深度研究模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    query = fields.TextField(description="用户研究查询")
    search_queries = fields.JSONField(description="搜索查询列表", default=[])
    contexts = fields.JSONField(description="收集的上下文信息", default=[])
    # literatures = fields.JSONField(description="参考文献列表", default=None, null=True)
    report_content = fields.TextField(description="最终生成的研究报告内容", null=True)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    iterations = fields.IntField(default=0, description="已完成的迭代次数")
    status = fields.CharEnumField(
        ResearchStatus,
        default=ResearchStatus.PENDING, 
        description="研究状态"
    )
    
    # 外键关联
    # api_key = fields.ForeignKeyField(
    #     "models.APIKey", 
    #     related_name="researches", 
    #     description="对应使用该Key的用户",
    #     null=True
    # )
    
    class Meta:
        table = "researches"
        description = "深度研究"
    
    def __str__(self):
        return f"Research {self.id} ({self.status})"


class ResearchResource(Model):
    """研究资源模型，存储搜索到的资源"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    research = fields.ForeignKeyField(
        "models.Research",
        related_name="resources",
        description="关联的研究"
    )
    url = fields.CharField(max_length=2048, description="资源URL")
    title = fields.CharField(max_length=512, null=True, description="资源标题")
    content = fields.TextField(description="资源原始内容", null=True) 
    extracted_context = fields.TextField(description="提取的相关上下文", null=True)
    search_query = fields.CharField(max_length=512, description="用于发现此资源的搜索查询")
    is_useful = fields.BooleanField(default=False, description="资源是否有用")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "research_resources"
        description = "研究资源"
    
    def __str__(self):
        return f"Resource {self.id} for Research {self.research_id}" 