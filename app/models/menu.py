from tortoise import fields
from tortoise.models import Model

class Menu(Model):
    id = fields.UUIDField(pk=True)
    name = fields.CharField(max_length=100, description="菜单名称")
    identifier = fields.CharField(max_length=100, description="菜单标识符")
    order = fields.IntField(default=0, description="菜单顺序")
    parent_id = fields.UUIDField(null=True, description="父菜单ID")
    # module = fields.ForeignKeyField('models.Module', related_name='menus', description="所属模块")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "menus"
        table_description = "菜单表" 