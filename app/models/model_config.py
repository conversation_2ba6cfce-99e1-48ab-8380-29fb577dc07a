import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone

from app.models.user import User


class ModelConfig(Model):
    """模型配置表，用于存储模型相关的配置信息"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    model_name = fields.CharField(max_length=100, description="模型名称（英文标识）")
    name = fields.CharField(max_length=255, null=True, description="模型显示名称")
    api_key = fields.CharField(max_length=255, description="调用模型所需的 API 密钥")
    api_url = fields.TextField(description="API 链接")
    max_context = fields.IntField(default=0, description="模型支持的最大上下文长度")
    max_output = fields.IntField(default=0, description="模型的最大输出上限")
    description = fields.TextField(null=True, description="模型的描述或用途")
    is_active = fields.BooleanField(default=True, description="是否启用模型")
    user = fields.ForeignKeyField('models.User', related_name='model_configs', description="关联的用户ID")
    created_at = fields.DatetimeField(auto_now_add=True, description="记录创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="记录更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    
    class Meta:
        table = "model_configs"
        description = "模型配置表，用于存储模型相关的配置信息"
    
    def __str__(self):
        return f"{self.model_name} ({self.name})" 