from tortoise import fields
from tortoise.models import Model
from datetime import datetime

class OrganizationRoleMenu(Model):
    id = fields.UUIDField(pk=True)
    # 如果组织没有就是超级管理员创建的无归属组织的用户的菜单
    organization = fields.ForeignKeyField('models.Organizations', null=True, related_name='role_menus', description="机构")
    role = fields.ForeignKeyField('models.Role', related_name='organization_menus', description="角色")
    menu = fields.ForeignKeyField('models.Menu', related_name='organization_roles', description="菜单")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "organization_role_menus"
        table_description = "机构角色菜单关联表"