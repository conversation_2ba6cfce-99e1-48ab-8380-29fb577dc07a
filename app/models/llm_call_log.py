from tortoise import fields
from tortoise.models import Model
from app.utils.constants import ProductType

class LlmCallLog(Model):
    id = fields.UUIDField(pk=True, description="主键")
    model_name = fields.CharField(max_length=128, description="模型名称")
    model_api_key = fields.CharField(max_length=256, description="模型密钥")
    model_api_url = fields.CharField(max_length=256, description="模型API地址")
    prompt = fields.JSONField(description="大模型调用的提示词", null=True)
    product_type = fields.CharEnumField(
        ProductType,
        description="调用方的产品类型说明"
    )
    # 比如材料ID、笔记ID、灵感ID
    related_id = fields.UUIDField(null=True, description="调用大模型的生成内容的记录主ID")
    response = fields.TextField(description="大模型返回的完整结果", null=True)
    user = fields.ForeignKeyField('models.User', related_name='llm_log_related_user', description="调用大模型的用户ID")
    description = fields.CharField(max_length=256, null=True, description="调用的描述信息")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "llm_call_logs"
        table_description = "大模型调用日志表" 