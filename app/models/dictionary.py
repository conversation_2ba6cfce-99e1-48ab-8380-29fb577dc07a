from tortoise import fields
from tortoise.models import Model

class Dictionary(Model):
    id = fields.UUIDField(pk=True)
    value = fields.CharField(max_length=50, description="字典值")
    remark = fields.CharField(null=True, max_length=300, description="附件信息")
    label = fields.CharField(max_length=50, description="展示标签")
    category = fields.CharField(max_length=50, description="分类标签")
    category_value = fields.CharField(max_length=50, description="分类值")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间", use_tz=True)
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "dictionary"
        table_description = "字典表" 