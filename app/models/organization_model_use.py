from tortoise import fields
from tortoise.models import Model
from enum import Enum


class UseCase(str, Enum):
    """产品类型枚举"""
    # 这是项目配置需要用到的模型，场景包括：AI课题名称优化、AI介绍、AI总结服务
    PROJECT_CONFIG_NEED = "PROJECT_CONFIG_NEED"
    # 这是生成大纲、生成报告的默认模型
    PAPER_GENERATE = "PAPER_GENERATE"
    # 扩写、续写、缩写、润色
    HANDLE_TEXT = "HANDLE_TEXT"
    # 脑图、重点服务的默认模型
    INSIGHT_MIND_MAP = "INSIGHT_MIND_MAP"
    # 生成灵感使用的模型
    INSIGHT_GENERATE = "INSIGHT_GENERATE"
    # 灵感里面的扩写、续写、缩写、润色、翻译服务的默认模型
    INSIGHT_HANDLE_TEXT = "INSIGHT_HANDLE_TEXT"


class OrganizationModelUses(Model):
    id = fields.UUIDField(pk=True)
    organization = fields.ForeignKeyField('models.Organizations', related_name='use_related_model', description="机构")
    model = fields.ForeignKeyField('models.ModelConfig', related_name='use_related_organization', description="模型")
    default_way = fields.CharEnumField(
        UseCase,
        description="模型的默认用途"
    )
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "organization_model_uses"
        table_description = "机构模型的用途表"