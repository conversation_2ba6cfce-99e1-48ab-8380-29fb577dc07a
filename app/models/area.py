from tortoise import fields
from tortoise.models import Model

class Area(Model):
    id = fields.IntField(pk=True)
    parent_id = fields.IntField(null=True)
    name = fields.CharField(max_length=100, description="名称")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间", use_tz=True)
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "area"
        table_description = "省市区表" 