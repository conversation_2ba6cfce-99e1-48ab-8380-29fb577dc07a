import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class User(Model):
    """管理员用户模型，仅用于后台管理"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    username = fields.Char<PERSON>ield(max_length=50,null=True, description="管理员用户名")
    hashed_password = fields.CharField(max_length=255, description="加密存储的密码")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    realname = fields.CharField(max_length=50, null=True, description="真实姓名")
    mobile = fields.Char<PERSON>ield(max_length=50, null=True, description="手机号码")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(auto_now=True, description="删除时间")
    # role_name = fields.Char<PERSON><PERSON>(max_length=255, description="角色名称")
    role = fields.ForeignKeyField(
        "models.Role", 
        related_name="user_role", 
        null=True, 
        description="角色ID"
    )
    organization = fields.ForeignKeyField(
        "models.Organizations", 
        related_name="user_organization", 
        null=True, 
        description="所属机构"
    )
    class Meta:
        table = "users"
        description = "后台管理用户"
    
    def __str__(self):
        return f"{self.username}" 