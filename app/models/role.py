from tortoise import fields
from tortoise.models import Model
from datetime import datetime

class Role(Model):
    id = fields.UUIDField(pk=True)
    name = fields.CharField(max_length=100, description="角色名称")
    identifier = fields.CharField(max_length=100, description="角色标识符")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    organization = fields.ForeignKeyField(
        "models.Organizations", 
        related_name="roles", 
        null=True, 
        description="所属机构"
    )

    class Meta:
        table = "roles"
        table_description = "角色表" 