from typing import Optional
from app.core.logging import get_logger
from app.services.llm_service import call_llm, call_llm_with_format_json
from app.utils.utils import send_data, ResponseModel
from app.models.model_config import ModelConfig
# from app.models.user import User
from app.api.schemas.user import UserResponse
from app.services.prompts import generate_optimize_project_name_prompt
from uuid import UUID
# 获取logger实例
logger = get_logger(__name__)
from app.api.schemas.model_config import ModelConfigBase

class ProductConfigsService:
    
    async def get_default_model(self, user: UserResponse) -> Optional[ModelConfig]:
        """
        获取用户默认的模型配置
        
        Args:
            user: 用户对象
            
        Returns:
            Optional[ModelConfig]: 用户的默认模型配置，如果不存在则返回None
        """
        try:
            # 获取用户启用的模型配置
            model = await ModelConfig.filter(
                user_id=user.id,
                is_active=True,
                is_deleted=False
            ).first()
            return model
        except Exception as e:
            return None

    async def optimize_project_name(
        self, 
        project_configs_name: str,
        model: ModelConfigBase
    ) -> ResponseModel[str]:
        """
        使用LLM模型优化项目名称
        
        Args:
            project_configs_name: 原始项目名称
            user: 用户对象
            model: 如果提供则使用指定的模型配置
            
        Returns:
            ResponseModel[str]: 包含优化后的项目名称的响应
        """
        try:
            # # 获取模型配置，优先使用指定的模型配置ID
            # model_config = None
            # if model_config_id:
            #     model_config = await ModelConfig.filter(
            #         id=model_config_id,
            #         user_id=user.id,
            #         is_deleted=False
            #     ).first()
            
            # # 如果没有指定模型配置ID或指定的模型配置不存在，则使用默认模型配置
            # if not model_config:
            #     model_config = await self.get_default_model(user)
                
            # if not model_config:
            #     return send_data(False, None, "未找到用户可用的模型配置")
            
            # 使用prompts.py中的函数生成提示词
            messages = generate_optimize_project_name_prompt(project_configs_name)
            
            # 定义响应格式 - 简单的字符串数组
            response_format = {
                "type": "json_schema",
                "json_schema": {
                    "name": "project_names_result",
                    "strict": True,
                    "schema": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "优化后的项目名称建议（不超过50字）"
                        }
                    }
                }
            }
            
            # 调用LLM服务，使用 call_llm_with_format_json 替代 call_llm
            optimized_names = await call_llm_with_format_json(
                messages=messages,
                model=model.model_name,
                api_key=model.api_key,
                api_url=model.api_url,
                response_format=response_format
            )
            
            if not optimized_names:
                return send_data(False, None, "LLM服务调用返回为空")
            
            # optimized_names应该已经是干净的JSON字符串，直接返回
            logger.info(f"优化后的项目名称: {optimized_names}")
            
            return send_data(True, optimized_names)
            
        except Exception as e:
            return send_data(False, None, f"优化项目名称失败: {str(e)}") 