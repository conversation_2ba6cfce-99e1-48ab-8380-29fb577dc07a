import os
import random
import json
from typing import Optional

from alibabacloud_dysmsapi20180501.client import Client as DysmsapiClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20180501 import models as dysmsapi_models

from app.core.config import settings
from app.core.redis_client import get_redis_client
from app.core.logging import logger


class SMSService:
    def __init__(self):
        self.client = self._create_client()
        self.redis_client = get_redis_client()
        
    def _create_client(self) -> DysmsapiClient:
        """创建阿里云短信客户端"""
        config = open_api_models.Config(
            access_key_id=settings.ALIBABA_CLOUD_ACCESS_KEY_ID,
            access_key_secret=settings.ALIBABA_CLOUD_ACCESS_KEY_SECRET
        )
        config.endpoint = 'dysmsapi.aliyuncs.com'
        return DysmsapiClient(config)
    
    def _generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        return str(random.randint(100000, 999999))
    
    def _get_last_send_key(self, mobile: str) -> str:
        """获取上次发送时间的Redis key"""
        return f"sms_last_send:{mobile}"
    
    def _get_verification_code_key(self, mobile: str) -> str:
        """获取验证码的Redis key"""
        return f"sms_code:{mobile}"
    
    async def _can_send_sms(self, mobile: str) -> bool:
        """检查是否可以发送短信（防止频繁发送）"""
        last_send_key = self._get_last_send_key(mobile)
        last_send_time = await self.redis_client.get(last_send_key)
        
        if last_send_time is None:
            return True
            
        import time
        current_time = int(time.time())
        last_time = int(last_send_time)
        
        return current_time - last_time >= settings.SMS_SEND_INTERVAL
    
    async def _record_send_time(self, mobile: str):
        """记录发送时间"""
        import time
        current_time = int(time.time())
        last_send_key = self._get_last_send_key(mobile)
        await self.redis_client.set(
            last_send_key, 
            current_time, 
            ex=settings.SMS_SEND_INTERVAL
        )
    
    async def send_verification_code(self, mobile: str) -> dict:
        """发送短信验证码"""
        try:
            # 检查发送频率限制
            if not await self._can_send_sms(mobile):
                return {
                    "success": False,
                    "message": f"发送过于频繁，请等待{settings.SMS_SEND_INTERVAL}秒后再试",
                    "request_id": None
                }
            
            # 生成验证码
            code = self._generate_verification_code()
            logger.info(f"验证码: {code}")
            
            # 构建发送请求
            send_sms_request = dysmsapi_models.SendMessageWithTemplateRequest(
                to=mobile,
                from_=settings.SMS_SIGN_NAME,
                template_code=settings.SMS_TEMPLATE_CODE,
                template_param=json.dumps({"code": code})
            )
            
            # 发送短信
            response = await self.client.send_message_with_template_async(
                send_sms_request
            )

            # 检查发送结果
            if response.body.code == "OK":
                # 存储验证码到Redis
                code_key = self._get_verification_code_key(mobile)
                await self.redis_client.set(
                    code_key, 
                    code, 
                    ex=settings.SMS_CODE_EXPIRE_TIME
                )
                
                # 记录发送时间
                await self._record_send_time(mobile)
                
                logger.info(f"短信验证码发送成功: {mobile}")
                return {
                    "success": True,
                    "message": "验证码发送成功",
                    "request_id": response.body.request_id
                }
            else:
                logger.error(f"短信发送失败: {mobile}, 错误: {response.body.message}")
                return {
                    "success": False,
                    "message": f"发送失败: {response.body.message}",
                    "request_id": response.body.request_id
                }
                
        except Exception as e:
            logger.error(f"发送短信异常: {mobile}, 错误: {str(e)}")
            return {
                "success": False,
                "message": "发送失败，请稍后重试",
                "request_id": None
            }
    
    async def verify_code(self, mobile: str, code: str) -> bool:
        """验证短信验证码"""
        try:
            code_key = self._get_verification_code_key(mobile)
            stored_code = await self.redis_client.get(code_key)
            
            if stored_code is None:
                logger.warning(f"验证码已过期或不存在: {mobile}")
                return False
            
            if stored_code == code:
                # 验证成功后删除验证码
                await self.redis_client.delete(code_key)
                logger.info(f"验证码验证成功: {mobile}")
                return True
            else:
                logger.warning(f"验证码错误: {mobile}")
                return False
                
        except Exception as e:
            logger.error(f"验证码验证异常: {mobile}, 错误: {str(e)}")
            return False


# 全局SMS服务实例
sms_service = SMSService()