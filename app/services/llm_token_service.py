import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, Any, Tuple
import os
from datetime import datetime, timezone

from app.core.config import settings
from app.core.logging import get_logger
from app.api.schemas.llm_token_response import OpenRouterGenerationData, OpenRouterResponse, LLMGenerationResponse
import time
# 获取logger实例
logger = get_logger(__name__)

async def test_model_connectivity(api_key: str, api_url: str, model: str) -> Tuple[bool, str]:
    """
    测试模型API的连通性
    
    Args:
        api_key: API密钥
        api_url: API地址
        model: 模型名称
        
    Returns:
        元组 (是否连通, 错误信息)，连通成功时错误信息为空字符串
    """
    try:
        logger.info(f"测试模型API连通性: {api_url}, 模型: {model}")
        
        # 准备简单的测试请求
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 构建简单消息体进行连通性测试
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": "测试连接"}],
            "stream": False
        }
        
        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"模型API连接失败: {error_text}")
                    return False, f"模型API连接失败！"
                
                # 连接成功
                logger.info("模型API连接测试成功")
                return True, ""
                
    except aiohttp.ClientError as e:
        error_msg = f"模型API连接失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"模型API连接测试失败: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False, error_msg

async def get_generation_result(generation_id: str,
                                api_url: str = settings.OPENROUTER_API_URL,
                                api_key: str = ""
                                ) -> LLMGenerationResponse:
    time.sleep(2)
    """
    获取特定生成任务的结果
    
    Args:
        generation_id: 生成任务的ID
        
    Returns:
        包含生成结果的LLMGenerationResponse模型
    """
    try:
        logger.info(f"获取生成任务结果, ID: {generation_id}")
        # 构建请求URL
        url = f"{api_url}/generation?id={generation_id}"
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "X-Title": "Hi-IdeaGen",
            "Content-Type": "application/json"
        }
        
        # 设置超时
        timeout = aiohttp.ClientTimeout(total=60)  # 60秒超时
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送请求: {url}")
            async with session.get(
                url,
                headers=headers,
                timeout=timeout
            ) as resp:
                response_text = await resp.text()
                
                # 尝试解析响应为JSON
                try:
                    result = json.loads(response_text)
                except json.JSONDecodeError:
                    logger.error(f"无法解析响应为JSON: {response_text}")
                    return LLMGenerationResponse(
                        success=False,
                        error="无法解析API响应"
                    )
                
                # 检查错误响应
                if resp.status != 200 or "error" in result:
                    error_message = "未知错误"
                    error_code = resp.status
                    
                    # 从响应中提取错误信息
                    if "error" in result:
                        error_message = result.get("error", {}).get("message", "未知错误")
                        error_code = result.get("error", {}).get("code", resp.status)
                    
                    logger.error(f"OpenRouter API 错误: 状态码 {error_code}")
                    logger.error(f"错误详情: {error_message}")
                    
                    return LLMGenerationResponse(
                        success=False,
                        error=f"生成任务获取失败: {error_message}",
                        error_code=error_code
                    )
                
                # 处理成功响应
                if "data" in result:
                    try:
                        # 使用Pydantic模型验证和解析数据
                        response = OpenRouterResponse(**result)
                        logger.info(f"成功获取生成任务结果: {generation_id}")
                        return LLMGenerationResponse(
                            success=True,
                            data=response.data
                        )
                    except Exception as e:
                        logger.error(f"解析响应数据失败: {str(e)}")
                        return LLMGenerationResponse(
                            success=False,
                            error=f"解析响应数据失败: {str(e)}"
                        )
                
                # 直接返回结果（兼容不同响应格式）
                logger.info(f"成功获取生成任务结果: {generation_id}")
                return LLMGenerationResponse(
                    success=True,
                    data=result
                )
                
    except asyncio.TimeoutError:
        logger.error(f"OpenRouter API 请求超时 (60秒)")
        return LLMGenerationResponse(
            success=False,
            error="API请求超时"
        )
    except aiohttp.ClientError as e:
        logger.error(f"aiohttp 客户端错误: {e.__class__.__name__}: {str(e)}")
        return LLMGenerationResponse(
            success=False,
            error=f"HTTP客户端错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"获取生成结果失败: {e.__class__.__name__}: {str(e)}")
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return LLMGenerationResponse(
            success=False,
            error=f"获取生成结果失败: {str(e)}"
        )

