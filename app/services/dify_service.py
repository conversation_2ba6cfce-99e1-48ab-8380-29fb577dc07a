import json
import aiohttp
import asyncio
from typing import Dict, List, Optional, Any, AsyncGenerator, Union
from app.core.config import settings
from app.core.logging import get_logger
from pydantic import BaseModel
from typing import Literal
from enum import Enum

class ContentStatus(str, Enum):
  """内容状态"""
  ERROR = "error"  # 错误状态
  NORMAL = "normal"  # 正常文本
  HEART_BEAT = "heart_beat"

class FileItem(BaseModel):
  type: Literal[
    "document","image","audio","video","custom"
  ]
  transfer_method: Literal["remote_url", "local_file"]
  url: Optional[str] = None  # 如果是 remote_url，填这个
  upload_file_id: Optional[str] = None  # 如果是 local_file，填这个
# 获取logger实例
logger = get_logger(__name__)


class DifyService:
    """Dify API服务类"""
    
    def __init__(self, api_key: str, base_url: str):
        """
        初始化Dify服务
        
        Args:
            api_key: Dify API密钥
            base_url: Dify API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    async def send_chat_message(
        self,
        query: str,
        user: str,
        inputs: Optional[Dict[str, Any]] = None,
        response_mode: str = "streaming",
        conversation_id: Optional[str] = None,
        files: Optional[List[FileItem]] = None,
        auto_generate_name: bool = True
    ) -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """
        发送对话消息到Dify API
        
        Args:
            query: 用户输入/提问内容
            user: 用户标识，应用内唯一
            inputs: 允许传入App定义的各变量值
            response_mode: 响应模式，streaming或blocking
            conversation_id: 会话ID，用于继续之前的对话
            files: 文件列表，仅当模型支持Vision能力时可用
            auto_generate_name: 自动生成会话标题
            
        Returns:
            - 如果response_mode为"blocking": 返回完整的API响应字典
            - 如果response_mode为"streaming": 返回AsyncGenerator[str, None]，可用于FastAPI StreamingResponse
        """
        logger.info(f"发送Dify对话消息, 用户: {user}, 响应模式: {response_mode}")
        
        # 构建请求体
        payload = {
            "query": query,
            "user": user,
            "response_mode": response_mode,
            "auto_generate_name": auto_generate_name
        }
        
        if inputs:
            payload["inputs"] = inputs
        if conversation_id:
            payload["conversation_id"] = conversation_id
        if files:
            # 将FileItem对象转换为字典
            payload["files"] = [file.dict() for file in files]
        
        # 设置超时
        timeout = aiohttp.ClientTimeout(total=3000)  # 50分钟
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/chat-messages"
                logger.debug(f"向Dify发送请求: {url}")
                
                async with session.post(
                    url,
                    headers=self.headers,
                    json=payload,
                    timeout=timeout
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"Dify API 错误: 状态码 {resp.status}")
                        logger.error(f"错误详情: {error_text}")
                        raise Exception(f"Dify API错误: {resp.status} - {error_text}")
                    
                    # 处理不同响应模式
                    if response_mode == "blocking":
                        # 阻塞模式，直接返回完整响应
                        result = await resp.json()
                        logger.info(f"Dify阻塞响应成功，消息ID: {result.get('message_id', 'N/A')}")
                        return result
                    else:
                        # 流式模式，返回异步生成器
                        return self._stream_response(resp)   
        except asyncio.TimeoutError:
            error_msg = "Dify API请求超时"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Dify API调用失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    async def _stream_response(self, response) -> AsyncGenerator[str, None]:
        """
        流式响应生成器，用于FastAPI StreamingResponse
        
        Args:
            response: aiohttp响应对象
            
        Yields:
            SSE格式的数据行
        """
        logger.debug("开始流式处理Dify响应")
        
        try:
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                if not line_str:
                    continue
                
                # 处理SSE格式
                if line_str.startswith("data: "):
                    data_str = line_str[6:]  # 移除"data: "前缀
                    
                    # 处理结束标记
                    if data_str == "[DONE]":
                        logger.debug("流式响应结束")
                        yield f"data: {json.dumps({'status': 'completed'})}\n\n"    
                        break
                    
                    try:
                        # 验证JSON格式
                        event_data = json.loads(data_str)
                        event_type = event_data.get("event", "")
                        logger.debug(f"转发事件: {event_type}")
                        
                        if event_type == "error":
                          error_msg = f"Dify返回错误: {event_data}"
                          logger.error(error_msg)
                          yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
                          break
                        else:
                          # 转发原始SSE数据
                          # yield f"data: {data_str}\n\n"
                          yield f"data: {json.dumps({'content':data_str, 'status': ContentStatus.NORMAL.value})}\n\n"   
                    except json.JSONDecodeError as e:
                        logger.warning(f"无法解析SSE数据: {data_str}, 错误: {e}")
                        continue         
        except Exception as e:
            error_msg = f"流式响应处理失败: {str(e)}"
            logger.error(error_msg)
            # 发送错误事件
            yield f"data: {json.dumps({'status': ContentStatus.ERROR.value, 'content': error_msg})}\n\n"
        
        logger.info("Dify流式响应处理完成")

    async def get_conversations(self, user: str, first_id: Optional[str] = None, limit: int = 20) -> Dict[str, Any]:
        """
        获取会话列表
        
        Args:
            user: 用户标识
            first_id: 分页起始ID
            limit: 返回数量限制
            
        Returns:
            会话列表
        """
        logger.info(f"获取用户会话列表: {user}")
        
        params = {"user": user, "limit": limit}
        if first_id:
            params["first_id"] = first_id
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/conversations"
                
                async with session.get(
                    url,
                    headers=self.headers,
                    params=params
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"获取会话列表失败: {resp.status} - {error_text}")
                        raise Exception(f"获取会话列表失败: {resp.status}")
                    
                    result = await resp.json()
                    logger.info(f"成功获取会话列表，数量: {len(result.get('data', []))}")
                    return result
                    
        except Exception as e:
            logger.error(f"获取会话列表失败: {str(e)}")
            raise

    

# 便捷函数，用于快速创建和使用Dify服务
async def create_dify_service(api_key: str, base_url: str) -> DifyService:
    """
    创建Dify服务实例
    
    Args:
        api_key: Dify API密钥
        base_url: Dify API基础URL
        
    Returns:
        DifyService实例
    """
    return DifyService(api_key, base_url)

