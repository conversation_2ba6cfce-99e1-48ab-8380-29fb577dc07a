from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "area" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "parent_id" INT,
    "name" VARCHAR(100) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "area"."name" IS '名称';
COMMENT ON COLUMN "area"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "area"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "area"."updated_at" IS '更新时间';
COMMENT ON COLUMN "area"."created_at" IS '创建时间';
COMMENT ON TABLE "area" IS '省市区表';
CREATE TABLE IF NOT EXISTS "dictionary" (
    "id" UUID NOT NULL PRIMARY KEY,
    "value" VARCHAR(50) NOT NULL,
    "remark" VARCHAR(300),
    "label" VARCHAR(50) NOT NULL,
    "category" VARCHAR(50) NOT NULL,
    "category_value" VARCHAR(50) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "dictionary"."value" IS '字典值';
COMMENT ON COLUMN "dictionary"."remark" IS '附件信息';
COMMENT ON COLUMN "dictionary"."label" IS '展示标签';
COMMENT ON COLUMN "dictionary"."category" IS '分类标签';
COMMENT ON COLUMN "dictionary"."category_value" IS '分类值';
COMMENT ON COLUMN "dictionary"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "dictionary"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "dictionary"."updated_at" IS '更新时间';
COMMENT ON COLUMN "dictionary"."created_at" IS '创建时间';
COMMENT ON TABLE "dictionary" IS '字典表';
CREATE TABLE IF NOT EXISTS "menus" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "identifier" VARCHAR(100) NOT NULL,
    "order" INT NOT NULL DEFAULT 0,
    "parent_id" UUID,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "menus"."name" IS '菜单名称';
COMMENT ON COLUMN "menus"."identifier" IS '菜单标识符';
COMMENT ON COLUMN "menus"."order" IS '菜单顺序';
COMMENT ON COLUMN "menus"."parent_id" IS '父菜单ID';
COMMENT ON COLUMN "menus"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "menus"."updated_at" IS '更新时间';
COMMENT ON COLUMN "menus"."deleted_at" IS '删除时间';
COMMENT ON TABLE "menus" IS '菜单表';
CREATE TABLE IF NOT EXISTS "organizations" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "code" VARCHAR(50) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "is_active" BOOL NOT NULL DEFAULT True,
    "is_trial" BOOL DEFAULT False,
    "contact_person" VARCHAR(50) NOT NULL,
    "contact_phone" VARCHAR(20) NOT NULL,
    "contact_email" VARCHAR(100) NOT NULL,
    "limit_count" INT,
    "use_count" INT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "description" TEXT,
    "remarks" TEXT
);
COMMENT ON COLUMN "organizations"."name" IS '机构名称';
COMMENT ON COLUMN "organizations"."code" IS '机构代码(唯一)';
COMMENT ON COLUMN "organizations"."type" IS '机构类型';
COMMENT ON COLUMN "organizations"."is_active" IS '是否启用';
COMMENT ON COLUMN "organizations"."is_trial" IS '是否是体验机构';
COMMENT ON COLUMN "organizations"."contact_person" IS '联系人';
COMMENT ON COLUMN "organizations"."contact_phone" IS '联系电话';
COMMENT ON COLUMN "organizations"."contact_email" IS '电子邮箱';
COMMENT ON COLUMN "organizations"."limit_count" IS '机构的总可用次数';
COMMENT ON COLUMN "organizations"."use_count" IS '机构的使用次数';
COMMENT ON COLUMN "organizations"."created_at" IS '创建时间';
COMMENT ON COLUMN "organizations"."updated_at" IS '最后修改时间';
COMMENT ON COLUMN "organizations"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organizations"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organizations"."description" IS '机构描述';
COMMENT ON COLUMN "organizations"."remarks" IS '备注';
CREATE TABLE IF NOT EXISTS "organization_menus" (
    "id" UUID NOT NULL PRIMARY KEY,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "menu_id" UUID NOT NULL REFERENCES "menus" ("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_menus"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_menus"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_menus"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_menus"."menu_id" IS '菜单';
COMMENT ON COLUMN "organization_menus"."organization_id" IS '机构';
COMMENT ON TABLE "organization_menus" IS '机构菜单关联表';
CREATE TABLE IF NOT EXISTS "researches" (
    "id" UUID NOT NULL PRIMARY KEY,
    "query" TEXT NOT NULL,
    "search_queries" JSONB NOT NULL,
    "contexts" JSONB NOT NULL,
    "report_content" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "iterations" INT NOT NULL DEFAULT 0,
    "status" VARCHAR(9) NOT NULL DEFAULT 'pending'
);
COMMENT ON COLUMN "researches"."query" IS '用户研究查询';
COMMENT ON COLUMN "researches"."search_queries" IS '搜索查询列表';
COMMENT ON COLUMN "researches"."contexts" IS '收集的上下文信息';
COMMENT ON COLUMN "researches"."report_content" IS '最终生成的研究报告内容';
COMMENT ON COLUMN "researches"."created_at" IS '创建时间';
COMMENT ON COLUMN "researches"."updated_at" IS '更新时间';
COMMENT ON COLUMN "researches"."iterations" IS '已完成的迭代次数';
COMMENT ON COLUMN "researches"."status" IS '研究状态';
COMMENT ON TABLE "researches" IS '深度研究模型';
CREATE TABLE IF NOT EXISTS "literatures" (
    "id" UUID NOT NULL PRIMARY KEY,
    "title" VARCHAR(500) NOT NULL,
    "authors" VARCHAR(500) NOT NULL,
    "journal" VARCHAR(200) NOT NULL,
    "year" INT NOT NULL,
    "issue" VARCHAR(20),
    "volume" VARCHAR(20),
    "pages" VARCHAR(50),
    "doi" VARCHAR(200),
    "url" VARCHAR(200),
    "summary" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "research_id" UUID NOT NULL REFERENCES "researches" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "literatures"."id" IS '文献ID';
COMMENT ON COLUMN "literatures"."title" IS '文献标题';
COMMENT ON COLUMN "literatures"."authors" IS '作者，多个作者用逗号分隔';
COMMENT ON COLUMN "literatures"."journal" IS '期刊名称';
COMMENT ON COLUMN "literatures"."year" IS '发表年份';
COMMENT ON COLUMN "literatures"."issue" IS '期号';
COMMENT ON COLUMN "literatures"."volume" IS '卷号';
COMMENT ON COLUMN "literatures"."pages" IS '页码范围，如：123-145';
COMMENT ON COLUMN "literatures"."doi" IS 'DOI索引';
COMMENT ON COLUMN "literatures"."url" IS '文献网页链接';
COMMENT ON COLUMN "literatures"."summary" IS '文献总结';
COMMENT ON COLUMN "literatures"."created_at" IS '创建时间';
COMMENT ON COLUMN "literatures"."updated_at" IS '更新时间';
COMMENT ON COLUMN "literatures"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "literatures"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "literatures"."research_id" IS '关联的研究项目';
COMMENT ON TABLE "literatures" IS '文献模型';
CREATE TABLE IF NOT EXISTS "research_resources" (
    "id" UUID NOT NULL PRIMARY KEY,
    "url" VARCHAR(2048) NOT NULL,
    "title" VARCHAR(512),
    "content" TEXT,
    "extracted_context" TEXT,
    "search_query" VARCHAR(512) NOT NULL,
    "is_useful" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "research_id" UUID NOT NULL REFERENCES "researches" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "research_resources"."url" IS '资源URL';
COMMENT ON COLUMN "research_resources"."title" IS '资源标题';
COMMENT ON COLUMN "research_resources"."content" IS '资源原始内容';
COMMENT ON COLUMN "research_resources"."extracted_context" IS '提取的相关上下文';
COMMENT ON COLUMN "research_resources"."search_query" IS '用于发现此资源的搜索查询';
COMMENT ON COLUMN "research_resources"."is_useful" IS '资源是否有用';
COMMENT ON COLUMN "research_resources"."created_at" IS '创建时间';
COMMENT ON COLUMN "research_resources"."research_id" IS '关联的研究';
COMMENT ON TABLE "research_resources" IS '研究资源模型，存储搜索到的资源';
CREATE TABLE IF NOT EXISTS "roles" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "identifier" VARCHAR(100) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "organization_id" UUID REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "roles"."name" IS '角色名称';
COMMENT ON COLUMN "roles"."identifier" IS '角色标识符';
COMMENT ON COLUMN "roles"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "roles"."created_at" IS '创建时间';
COMMENT ON COLUMN "roles"."updated_at" IS '更新时间';
COMMENT ON COLUMN "roles"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "roles"."organization_id" IS '所属机构';
COMMENT ON TABLE "roles" IS '角色表';
CREATE TABLE IF NOT EXISTS "organization_role_menus" (
    "id" UUID NOT NULL PRIMARY KEY,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "menu_id" UUID NOT NULL REFERENCES "menus" ("id") ON DELETE CASCADE,
    "organization_id" UUID REFERENCES "organizations" ("id") ON DELETE CASCADE,
    "role_id" UUID NOT NULL REFERENCES "roles" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_role_menus"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_role_menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_role_menus"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_role_menus"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_role_menus"."menu_id" IS '菜单';
COMMENT ON COLUMN "organization_role_menus"."organization_id" IS '机构';
COMMENT ON COLUMN "organization_role_menus"."role_id" IS '角色';
COMMENT ON TABLE "organization_role_menus" IS '机构角色菜单关联表';
CREATE TABLE IF NOT EXISTS "upload_files" (
    "id" UUID NOT NULL PRIMARY KEY,
    "file_path" VARCHAR(255) NOT NULL,
    "file_name" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "upload_files"."file_path" IS '文件路径';
COMMENT ON COLUMN "upload_files"."file_name" IS '文件名称';
COMMENT ON COLUMN "upload_files"."created_at" IS '创建时间';
COMMENT ON COLUMN "upload_files"."updated_at" IS '更新时间';
COMMENT ON COLUMN "upload_files"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "upload_files"."deleted_at" IS '删除时间';
COMMENT ON TABLE "upload_files" IS '上传的文件';
CREATE TABLE IF NOT EXISTS "users" (
    "id" UUID NOT NULL PRIMARY KEY,
    "username" VARCHAR(50),
    "hashed_password" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "realname" VARCHAR(50),
    "mobile" VARCHAR(50),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "organization_id" UUID REFERENCES "organizations" ("id") ON DELETE CASCADE,
    "role_id" UUID REFERENCES "roles" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "users"."username" IS '管理员用户名';
COMMENT ON COLUMN "users"."hashed_password" IS '加密存储的密码';
COMMENT ON COLUMN "users"."created_at" IS '创建时间';
COMMENT ON COLUMN "users"."realname" IS '真实姓名';
COMMENT ON COLUMN "users"."mobile" IS '手机号码';
COMMENT ON COLUMN "users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "users"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "users"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "users"."organization_id" IS '所属机构';
COMMENT ON COLUMN "users"."role_id" IS '角色ID';
COMMENT ON TABLE "users" IS '管理员用户模型，仅用于后台管理';
CREATE TABLE IF NOT EXISTS "llm_call_logs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "model_name" VARCHAR(128) NOT NULL,
    "model_api_key" VARCHAR(256) NOT NULL,
    "model_api_url" VARCHAR(256) NOT NULL,
    "prompt" JSONB,
    "product_type" VARCHAR(11) NOT NULL,
    "related_id" UUID,
    "response" TEXT,
    "description" VARCHAR(256),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "llm_call_logs"."id" IS '主键';
COMMENT ON COLUMN "llm_call_logs"."model_name" IS '模型名称';
COMMENT ON COLUMN "llm_call_logs"."model_api_key" IS '模型密钥';
COMMENT ON COLUMN "llm_call_logs"."model_api_url" IS '模型API地址';
COMMENT ON COLUMN "llm_call_logs"."prompt" IS '大模型调用的提示词';
COMMENT ON COLUMN "llm_call_logs"."product_type" IS '调用方的产品类型说明';
COMMENT ON COLUMN "llm_call_logs"."related_id" IS '调用大模型的生成内容的记录主ID';
COMMENT ON COLUMN "llm_call_logs"."response" IS '大模型返回的完整结果';
COMMENT ON COLUMN "llm_call_logs"."description" IS '调用的描述信息';
COMMENT ON COLUMN "llm_call_logs"."created_at" IS '创建时间';
COMMENT ON COLUMN "llm_call_logs"."user_id" IS '调用大模型的用户ID';
COMMENT ON TABLE "llm_call_logs" IS '大模型调用日志表';
CREATE TABLE IF NOT EXISTS "model_configs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "model_name" VARCHAR(100) NOT NULL,
    "name" VARCHAR(255),
    "api_key" VARCHAR(255) NOT NULL,
    "api_url" TEXT NOT NULL,
    "max_context" INT NOT NULL DEFAULT 0,
    "max_output" INT NOT NULL DEFAULT 0,
    "description" TEXT,
    "is_active" BOOL NOT NULL DEFAULT True,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "model_configs"."model_name" IS '模型名称（英文标识）';
COMMENT ON COLUMN "model_configs"."name" IS '模型显示名称';
COMMENT ON COLUMN "model_configs"."api_key" IS '调用模型所需的 API 密钥';
COMMENT ON COLUMN "model_configs"."api_url" IS 'API 链接';
COMMENT ON COLUMN "model_configs"."max_context" IS '模型支持的最大上下文长度';
COMMENT ON COLUMN "model_configs"."max_output" IS '模型的最大输出上限';
COMMENT ON COLUMN "model_configs"."description" IS '模型的描述或用途';
COMMENT ON COLUMN "model_configs"."is_active" IS '是否启用模型';
COMMENT ON COLUMN "model_configs"."created_at" IS '记录创建时间';
COMMENT ON COLUMN "model_configs"."updated_at" IS '记录更新时间';
COMMENT ON COLUMN "model_configs"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "model_configs"."user_id" IS '关联的用户ID';
COMMENT ON TABLE "model_configs" IS '模型配置表，用于存储模型相关的配置信息';
CREATE TABLE IF NOT EXISTS "organization_model_uses" (
    "id" UUID NOT NULL PRIMARY KEY,
    "default_way" VARCHAR(19) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_model_uses"."default_way" IS '模型的默认用途';
COMMENT ON COLUMN "organization_model_uses"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_model_uses"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_model_uses"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_model_uses"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_model_uses"."model_id" IS '模型';
COMMENT ON COLUMN "organization_model_uses"."organization_id" IS '机构';
COMMENT ON TABLE "organization_model_uses" IS '机构模型的用途表';
CREATE TABLE IF NOT EXISTS "organization_models" (
    "id" UUID NOT NULL PRIMARY KEY,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_models"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_models"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_models"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_models"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_models"."model_id" IS '模型';
COMMENT ON COLUMN "organization_models"."organization_id" IS '机构';
COMMENT ON TABLE "organization_models" IS '机构模型关联表';
CREATE TABLE IF NOT EXISTS "project_configs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(300),
    "application_category" VARCHAR(50),
    "ai_leader_introduction" VARCHAR(2000),
    "word_count_requirement" INT,
    "literature_library" TEXT,
    "requirements_attachments" JSONB,
    "language_style" VARCHAR(100),
    "estimated_time" TIMESTAMPTZ,
    "ai_generated_outline" TEXT,
    "outline_generated_time" TIMESTAMPTZ,
    "manual_modified_outline" TEXT,
    "manual_modified_outline_time" TIMESTAMPTZ,
    "ai_generated_report" TEXT,
    "report_generation_time" TIMESTAMPTZ,
    "manual_modified_report" TEXT,
    "manual_modified_report_time" TIMESTAMPTZ,
    "outline_tokens_consumed" INT DEFAULT 0,
    "report_tokens_consumed" INT DEFAULT 0,
    "ai_trace_report" TEXT,
    "ai_trace_generated_time" TIMESTAMPTZ,
    "hallucination_report" TEXT,
    "hallucination_generated_time" TIMESTAMPTZ,
    "user_add_prompt" TEXT,
    "status" VARCHAR(20) NOT NULL DEFAULT 'CONFIGURING',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" INT NOT NULL DEFAULT 0,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "research_id" UUID REFERENCES "researches" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
    "user_add_demo_id" UUID REFERENCES "upload_files" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "project_configs"."name" IS '项目名称';
COMMENT ON COLUMN "project_configs"."application_category" IS '申报口径';
COMMENT ON COLUMN "project_configs"."ai_leader_introduction" IS '项目主体的AI介绍';
COMMENT ON COLUMN "project_configs"."word_count_requirement" IS '字数要求';
COMMENT ON COLUMN "project_configs"."literature_library" IS '文献库';
COMMENT ON COLUMN "project_configs"."requirements_attachments" IS '申报要求附件';
COMMENT ON COLUMN "project_configs"."language_style" IS '语言风格';
COMMENT ON COLUMN "project_configs"."estimated_time" IS '预估时间';
COMMENT ON COLUMN "project_configs"."ai_generated_outline" IS 'AI生成的大纲';
COMMENT ON COLUMN "project_configs"."outline_generated_time" IS '大纲生成时间';
COMMENT ON COLUMN "project_configs"."manual_modified_outline" IS '人工修改的大纲';
COMMENT ON COLUMN "project_configs"."manual_modified_outline_time" IS '人工修改大纲的时间';
COMMENT ON COLUMN "project_configs"."ai_generated_report" IS 'AI生成的报告';
COMMENT ON COLUMN "project_configs"."report_generation_time" IS '报告的生成时间';
COMMENT ON COLUMN "project_configs"."manual_modified_report" IS '人工修改的报告';
COMMENT ON COLUMN "project_configs"."manual_modified_report_time" IS '人工修改报告的时间';
COMMENT ON COLUMN "project_configs"."outline_tokens_consumed" IS '大纲生成消耗的tokens数量';
COMMENT ON COLUMN "project_configs"."report_tokens_consumed" IS '报告生成消耗的tokens数量';
COMMENT ON COLUMN "project_configs"."ai_trace_report" IS 'AI痕迹的报告内容';
COMMENT ON COLUMN "project_configs"."ai_trace_generated_time" IS 'AI痕迹报告的生成时间';
COMMENT ON COLUMN "project_configs"."hallucination_report" IS '幻觉审查的报告内容';
COMMENT ON COLUMN "project_configs"."hallucination_generated_time" IS '幻觉审查的生成时间';
COMMENT ON COLUMN "project_configs"."user_add_prompt" IS '用户增加的提示词';
COMMENT ON COLUMN "project_configs"."status" IS '项目状态 (\n        CONFIGURING,\n        OUTLINE_GENERATING,\n        OUTLINE_GENERATED,\n        OUTLINE_FAILED,\n        REPORT_GENERATING,\n        REPORT_GENERATED,\n        REPORT_FAILED,\n        REMOVE_HALLUCINATING,\n        REMOVE_HALLUCINATED,\n        REMOVE_AI_TRACING,\n        REMOVE_AI_TRACED\n        )';
COMMENT ON COLUMN "project_configs"."created_at" IS '创建时间';
COMMENT ON COLUMN "project_configs"."updated_at" IS '更新时间';
COMMENT ON COLUMN "project_configs"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "project_configs"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "project_configs"."model_id" IS '关联的模型配置';
COMMENT ON COLUMN "project_configs"."research_id" IS '关联研究表';
COMMENT ON COLUMN "project_configs"."user_id" IS '创建用户';
COMMENT ON COLUMN "project_configs"."user_add_demo_id" IS '关联文件上传表';
COMMENT ON TABLE "project_configs" IS '项目配置模型';
CREATE TABLE IF NOT EXISTS "literature_librarys" (
    "id" UUID NOT NULL PRIMARY KEY,
    "query" TEXT NOT NULL,
    "search_queries" JSONB NOT NULL,
    "contexts" JSONB NOT NULL,
    "report_content" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "iterations" INT NOT NULL DEFAULT 0,
    "status" VARCHAR(9) NOT NULL DEFAULT 'pending',
    "project_config_id" UUID REFERENCES "project_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "literature_librarys"."query" IS '文献查询内容';
COMMENT ON COLUMN "literature_librarys"."search_queries" IS '搜索查询列表';
COMMENT ON COLUMN "literature_librarys"."contexts" IS '收集的上下文信息';
COMMENT ON COLUMN "literature_librarys"."report_content" IS '最终生成的文献报告内容';
COMMENT ON COLUMN "literature_librarys"."created_at" IS '创建时间';
COMMENT ON COLUMN "literature_librarys"."updated_at" IS '更新时间';
COMMENT ON COLUMN "literature_librarys"."iterations" IS '已完成的迭代次数';
COMMENT ON COLUMN "literature_librarys"."status" IS '文献库状态';
COMMENT ON COLUMN "literature_librarys"."project_config_id" IS '关联的项目配置';
COMMENT ON COLUMN "literature_librarys"."user_id" IS '关联用户';
COMMENT ON TABLE "literature_librarys" IS '文献库模型';
CREATE TABLE IF NOT EXISTS "literature_library_resources" (
    "id" UUID NOT NULL PRIMARY KEY,
    "url" VARCHAR(2048) NOT NULL,
    "title" VARCHAR(512),
    "content" TEXT,
    "extracted_context" TEXT,
    "search_query" VARCHAR(512) NOT NULL,
    "is_useful" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "literature_library_id" UUID NOT NULL REFERENCES "literature_librarys" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "literature_library_resources"."url" IS '资源URL';
COMMENT ON COLUMN "literature_library_resources"."title" IS '资源标题';
COMMENT ON COLUMN "literature_library_resources"."content" IS '资源原始内容';
COMMENT ON COLUMN "literature_library_resources"."extracted_context" IS '提取的相关上下文';
COMMENT ON COLUMN "literature_library_resources"."search_query" IS '用于发现此资源的搜索查询';
COMMENT ON COLUMN "literature_library_resources"."is_useful" IS '资源是否有用';
COMMENT ON COLUMN "literature_library_resources"."created_at" IS '创建时间';
COMMENT ON COLUMN "literature_library_resources"."literature_library_id" IS '关联的文献库';
COMMENT ON TABLE "literature_library_resources" IS '文献库资源模型，存储搜索到的资源';
CREATE TABLE IF NOT EXISTS "docgen_project_url_summary" (
    "id" UUID NOT NULL PRIMARY KEY,
    "url" VARCHAR(512) NOT NULL,
    "summary" TEXT,
    "is_valid" BOOL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "project_id" UUID REFERENCES "project_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "docgen_project_url_summary"."id" IS '主键';
COMMENT ON COLUMN "docgen_project_url_summary"."url" IS '网页地址';
COMMENT ON COLUMN "docgen_project_url_summary"."summary" IS 'url的内容总结';
COMMENT ON COLUMN "docgen_project_url_summary"."is_valid" IS '是否有效，如果为None说明还没核验';
COMMENT ON COLUMN "docgen_project_url_summary"."created_at" IS '创建时间';
COMMENT ON COLUMN "docgen_project_url_summary"."updated_at" IS '更新时间';
COMMENT ON COLUMN "docgen_project_url_summary"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "docgen_project_url_summary"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "docgen_project_url_summary"."project_id" IS '项目ID';
COMMENT ON COLUMN "docgen_project_url_summary"."user_id" IS '创建用户';
COMMENT ON TABLE "docgen_project_url_summary" IS '项目用户自主输入的网页链接及内容总结表';
CREATE TABLE IF NOT EXISTS "user_default_models" (
    "id" UUID NOT NULL PRIMARY KEY,
    "default_way" VARCHAR(19) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "user_default_models"."default_way" IS '模型的默认用途';
COMMENT ON COLUMN "user_default_models"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "user_default_models"."created_at" IS '创建时间';
COMMENT ON COLUMN "user_default_models"."updated_at" IS '更新时间';
COMMENT ON COLUMN "user_default_models"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "user_default_models"."model_id" IS '模型';
COMMENT ON COLUMN "user_default_models"."user_id" IS '用户';
COMMENT ON TABLE "user_default_models" IS '用户的默认模型关联表';
CREATE TABLE IF NOT EXISTS "user_report_usages" (
    "id" UUID NOT NULL PRIMARY KEY,
    "used_count" INT NOT NULL DEFAULT 0,
    "max_allowed_count" INT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "user_id_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "user_report_usages"."used_count" IS '已使用生成报告次数';
COMMENT ON COLUMN "user_report_usages"."max_allowed_count" IS '最大允许生成报告次数';
COMMENT ON COLUMN "user_report_usages"."created_at" IS '创建时间';
COMMENT ON COLUMN "user_report_usages"."updated_at" IS '更新时间';
COMMENT ON COLUMN "user_report_usages"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "user_report_usages"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "user_report_usages"."user_id_id" IS '关联用户ID';
COMMENT ON TABLE "user_report_usages" IS '用户报告生成使用次数模型';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
