import os
import sys

print("当前路径:", os.getcwd())
print("sys.path:", sys.path)

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import json
from tortoise import Tortoise
from app.models.model_config import ModelConfig
from app.models.organization_model import OrganizationModels
from app.models.organization_model_use import OrganizationModelUses, UseCase
from dotenv import load_dotenv
from datetime import datetime
from app.models.project_configs import ProjectConfig
from app.models.organizations import Organizations

load_dotenv()  # 可从 .env 文件加载数据库连接

DATABASE_URL = os.getenv("DATABASE_URL")
print("DATABASE_URL:", DATABASE_URL)

async def run():
    await Tortoise.init(
        db_url=DATABASE_URL,
        modules={"models": ["app.models"]}
    )
    await Tortoise.generate_schemas()

    # 首先查处所有带有组织信息的用户创建的未被删除的模型
    all_model_config = await ModelConfig.filter(
      is_deleted=False,
      user__organization_id__not_isnull=True,
    ).prefetch_related("user").all()
    # 记录是因为model_name相同而被删除的所有模型id，用于后续修复替换
    model_deleted = []
    for model in all_model_config:
      # 首先查询model_name是不是已经存在了：
      model_exist = await OrganizationModels.filter(
        is_deleted=False,
        model__model_name=model.model_name
      ).first()
      # 如果不存在
      if not model_exist:
        # 给机构创建model和机构关联的中间表
        await OrganizationModels.create(
          organization_id=model.user.organization_id,
          model_id=model.id,
        )
      # 如果存在，判断一下id是不是一致，不一致则保存当前的model id到model_deleted
      elif model_exist.model_id != model.id:
        model_deleted.append({
           "deleted_id": model.id,
           "new_id": model_exist.model_id
        })
    # 将所有使用已经被删除模型的model进行修复
    for item in model_deleted:
      await ProjectConfig.filter(
        model_id=item["deleted_id"]
      ).update(
        model_id=item["new_id"]
      )
      # 将ModelConfig里面的对应数据清除
      await ModelConfig.filter(
        id=item["deleted_id"]
      ).update(
        is_deleted=True
      )
    # 查询所有的组织
    all_org = await Organizations.all()
    for org in all_org:
      org_model = await OrganizationModels.filter(
         organization_id=org.id,
         is_deleted=False
      ).first()
      if org_model:
        # 创建机构的默认用途的关联记录
        for item in UseCase:
           is_exist = await OrganizationModelUses.filter(
              organization_id=org_model.organization_id,
              default_way=item.value
           ).first()
           if not is_exist: 
            await OrganizationModelUses.create(
                organization_id=org_model.organization_id,
                model_id=org_model.model_id,
                default_way=item.value
            )
      
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(run())
