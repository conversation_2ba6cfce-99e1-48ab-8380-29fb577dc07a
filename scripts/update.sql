INSERT INTO roles (id, name, identifier)
VALUES ('a182b477-a674-49ca-80f0-fe7f2286c992', '超级管理员', 'super_admin');
INSERT INTO organizations (
    id,
    name,
    code,
    type,
    contact_person,
    contact_phone,
    contact_email
) VALUES (
    'e8bd89c6-4b6a-4483-a045-06840f175632',  -- 替换为你自己的 UUID
    '个人付费机构',
    'bran',
    '事业单位',
    'bran.qiu',
    '18616606125',
    '<EMAIL>'
);
INSERT INTO roles (id, name, identifier, organization_id)
VALUES ('9669897e-b2a9-46bb-bc6b-cc5436295786', '机构管理员', 'admin', 'e8bd89c6-4b6a-4483-a045-06840f175632');
INSERT INTO roles (id, name, identifier, organization_id)
VALUES ('e49d69af-b66a-47f5-a11a-067760e36335', '普通用户', 'client', 'e8bd89c6-4b6a-4483-a045-06840f175632');
INSERT INTO users (
    id,
    username,
    hashed_password,
    role_id,
    organization_id
) VALUES (
    'e49d69af-b66a-47f5-a11a-067760e36335',
    'bran_admin',                          
    '$2b$12$sczGe./9SFtUBXXjnkb2U.MYVGJoMZSwUdDTTuKrAu/9EGtBRJUom',
    '9669897e-b2a9-46bb-bc6b-cc5436295786',
    'e8bd89c6-4b6a-4483-a045-06840f175632' 
);
INSERT INTO user_report_usages (
    id,
    used_count,
    max_allowed_count,
    user_id_id
) VALUES (
    'e49d69af-b66a-47f5-a11a-067760e36335',
    0,                          
    null,
    'e49d69af-b66a-47f5-a11a-067760e36335' 
);
UPDATE users
SET role_id = 'a182b477-a674-49ca-80f0-fe7f2286c992'
WHERE username = 'idea_admin';
UPDATE users
SET
    organization_id = 'e8bd89c6-4b6a-4483-a045-06840f175632',
    role_id = 'e49d69af-b66a-47f5-a11a-067760e36335'
WHERE username NOT IN ('idea_admin', 'bran_admin');





-- 插入一级菜单 co-ai-agent  
INSERT INTO "public"."menus" (  
    "id",   
    "name",   
    "identifier",   
    "order",   
    "parent_id",   
    "is_deleted",   
    "created_at",   
    "updated_at"  
) VALUES (  
    gen_random_uuid(),   
    'Co_Ai Agent',   
    'co-ai-agent',   
    10,   
    NULL,   
    false,   
    CURRENT_TIMESTAMP,   
    CURRENT_TIMESTAMP  
);  

-- 插入二级菜单（需要先获取一级菜单的ID）  
WITH parent_menu AS (  
    SELECT id FROM "public"."menus" WHERE "identifier" = 'co-ai-agent'  
)  
INSERT INTO "public"."menus" (  
    "id",   
    "name",   
    "identifier",   
    "order",   
    "parent_id",   
    "is_deleted",   
    "created_at",   
    "updated_at"  
)  
SELECT   
    gen_random_uuid(),  
    menu_data.name,  
    menu_data.identifier,  
    menu_data."order",  
    parent_menu.id,  
    false,  
    CURRENT_TIMESTAMP,  
    CURRENT_TIMESTAMP  
FROM parent_menu,  
(VALUES   
    ('AI对话', 'ai-chat', 1),  
    ('AI PPT', 'ai-ppt', 2),  
    ('AI论文', 'ai-paper', 3),  
    ('AI理科作业助手', 'ai-homework', 4),  
    ('降低AI', 'ai-trace-removal', 5)  
) AS menu_data(name, identifier, "order");
