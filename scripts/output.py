import os
import sys

print("当前路径:", os.getcwd())
print("sys.path:", sys.path)

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import json
from tortoise import Tortoise
from app.models.dictionary import Dictionary
from dotenv import load_dotenv

load_dotenv()  # 可从 .env 文件加载数据库连接

DATABASE_URL = os.getenv("DATABASE_URL")

async def run():
    await Tortoise.init(
        db_url=DATABASE_URL,
        modules={"models": ["app.models"]}
    )
    await Tortoise.generate_schemas()

    data = await Dictionary.all().values()
    with open("scripts/data.json", "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(run())
