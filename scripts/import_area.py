import os
import sys

print("当前路径:", os.getcwd())
print("sys.path:", sys.path)

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from tortoise import Tortoise, run_async
from app.models.area import Area  # 请确保路径正确
from dotenv import load_dotenv

load_dotenv()  # 可从 .env 文件加载数据库连接

DATABASE_URL = os.getenv("DATABASE_URL")

async def init_orm():
    await Tortoise.init(
        db_url=DATABASE_URL,
        modules={"models": ["app.models"]}
    )
    await Tortoise.generate_schemas()

async def import_data():
    with open("scripts/area.json", "r", encoding="utf-8-sig") as f:
        data = json.load(f)

    async def insert_recursive(data: dict, parent_id: int = None):
        for code, node in data.items():
            name = node.get("n")
            area = await Area.create(name=name, parent_id=parent_id)
            sub_nodes = node.get("c")
            if sub_nodes:
                await insert_recursive(sub_nodes, parent_id=area.id)

    await insert_recursive(data)

async def run():
    await init_orm()
    await import_data()
    await Tortoise.close_connections()

if __name__ == "__main__":
    run_async(run())
