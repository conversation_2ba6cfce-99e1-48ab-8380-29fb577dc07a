#!/usr/bin/env python3
"""
测试SMS API接口的脚本
用于验证SMS接口是否能正常访问，不被JWT token拦截
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8001"  # 根据实际端口调整
SMS_SEND_CODE_URL = f"{BASE_URL}/api/sms/send-code"
SMS_MOBILE_LOGIN_URL = f"{BASE_URL}/api/sms/mobile-login"

def test_send_code_api():
    """测试发送验证码接口"""
    print("=" * 50)
    print("测试发送验证码接口")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "mobile": "13800138000"  # 测试手机号
    }
    
    try:
        # 发送请求（不带任何认证头）
        response = requests.post(
            SMS_SEND_CODE_URL,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"请求URL: {SMS_SEND_CODE_URL}")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 接口访问成功！")
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        elif response.status_code == 403:
            print("❌ 403 Forbidden - 接口被JWT token拦截！")
            print(f"响应内容: {response.text}")
        else:
            print(f"⚠️  其他错误 - 状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 其他异常: {str(e)}")

def test_mobile_login_api():
    """测试手机号登录接口"""
    print("\n" + "=" * 50)
    print("测试手机号登录接口")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "mobile": "13800138000",  # 测试手机号
        "code": "654321"  # 测试验证码（与SMS_TEST_CODE配置一致）
    }
    
    try:
        # 发送请求（不带任何认证头）
        response = requests.post(
            SMS_MOBILE_LOGIN_URL,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"请求URL: {SMS_MOBILE_LOGIN_URL}")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 接口访问成功！")
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        elif response.status_code == 403:
            print("❌ 403 Forbidden - 接口被JWT token拦截！")
            print(f"响应内容: {response.text}")
        else:
            print(f"⚠️  其他错误 - 状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 其他异常: {str(e)}")

def test_protected_api():
    """测试需要认证的接口，用于对比"""
    print("\n" + "=" * 50)
    print("测试需要认证的接口（对比测试）")
    print("=" * 50)
    
    protected_url = f"{BASE_URL}/api/users"  # 需要认证的接口
    
    try:
        # 发送请求（不带任何认证头）
        response = requests.get(
            protected_url,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"请求URL: {protected_url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ 需要认证的接口正常返回401！")
        elif response.status_code == 403:
            print("✅ 需要认证的接口正常返回403！")
        else:
            print(f"⚠️  意外的状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 其他异常: {str(e)}")

if __name__ == "__main__":
    print("SMS API 接口测试")
    print("请确保后端服务已启动在 http://localhost:8001")
    print()
    
    # 测试SMS接口
    test_send_code_api()
    test_mobile_login_api()
    
    # 测试需要认证的接口作为对比
    test_protected_api()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
