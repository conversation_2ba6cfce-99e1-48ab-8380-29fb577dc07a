import uuid
from enum import Enum
from tortoise import fields
from tortoise.models import Model
from datetime import datetime


class LiteratureLibraryStatus(str, Enum):
    """文献库状态枚举"""
    PENDING = "pending"        # 待处理
    SEARCHING = "searching"    # 搜索中
    ANALYZING = "analyzing"    # 分析中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"          # 失败


class LiteratureLibrary(Model):
    """文献库模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    query = fields.TextField(description="文献查询内容", default=[])
    search_queries = fields.JSONField(description="搜索查询列表", default=[])
    contexts = fields.JSONField(description="收集的上下文信息", default=[])
    report_content = fields.TextField(description="最终生成的文献报告内容", null=True)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    iterations = fields.IntField(default=0, description="已完成的迭代次数")
    status = fields.CharEnumField(
        LiteratureLibraryStatus, 
        default=LiteratureLibraryStatus.PENDING, 
        description="文献库状态"
    )
    
    # 外键关联
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="literature_libraries", 
        description="关联用户"
    )
    project_config = fields.ForeignKeyField(
        "models.ProjectConfig",
        related_name="literature_libraries",
        description="关联的项目配置",
        null=True,
        source_field="project_config_id"  # 明确指定源字段名称
    )
    
    class Meta:
        table = "literature_librarys"
        description = "文献库"
    
    def __str__(self):
        return f"LiteratureLibrary {self.id} ({self.status})"


class LiteratureLibraryResource(Model):
    """文献库资源模型，存储搜索到的资源"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    literature_library = fields.ForeignKeyField(
        "models.LiteratureLibrary",
        related_name="resources",
        description="关联的文献库"
    )
    url = fields.CharField(max_length=2048, description="资源URL")
    title = fields.CharField(max_length=512, null=True, description="资源标题")
    content = fields.TextField(description="资源原始内容", null=True) 
    extracted_context = fields.TextField(description="提取的相关上下文", null=True)
    search_query = fields.CharField(max_length=512, description="用于发现此资源的搜索查询")
    is_useful = fields.BooleanField(default=False, description="资源是否有用")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "literature_library_resources"
        description = "文献库资源"
    
    def __str__(self):
        return f"Resource {self.id} for LiteratureLibrary {self.literature_library_id}" 